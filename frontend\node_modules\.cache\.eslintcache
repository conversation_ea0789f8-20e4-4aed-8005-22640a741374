[{"D:\\mysql-project\\frontend\\src\\index.js": "1", "D:\\mysql-project\\frontend\\src\\App.js": "2", "D:\\mysql-project\\frontend\\src\\reportWebVitals.js": "3", "D:\\mysql-project\\frontend\\src\\components\\Register.js": "4", "D:\\mysql-project\\frontend\\src\\contexts\\AuthContext.js": "5", "D:\\mysql-project\\frontend\\src\\components\\Login.js": "6", "D:\\mysql-project\\frontend\\src\\components\\PrivateRoute.js": "7", "D:\\mysql-project\\frontend\\src\\components\\Dashboard.js": "8"}, {"size": 535, "mtime": 1754295223309, "results": "9", "hashOfConfig": "10"}, {"size": 1208, "mtime": 1754295674594, "results": "11", "hashOfConfig": "10"}, {"size": 362, "mtime": 1754295223650, "results": "12", "hashOfConfig": "10"}, {"size": 6550, "mtime": 1754295560529, "results": "13", "hashOfConfig": "10"}, {"size": 6736, "mtime": 1754295525628, "results": "14", "hashOfConfig": "10"}, {"size": 3115, "mtime": 1754295539312, "results": "15", "hashOfConfig": "10"}, {"size": 1582, "mtime": 1754295664051, "results": "16", "hashOfConfig": "10"}, {"size": 5320, "mtime": 1754295580995, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "armk0z", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\mysql-project\\frontend\\src\\index.js", [], [], "D:\\mysql-project\\frontend\\src\\App.js", [], [], "D:\\mysql-project\\frontend\\src\\reportWebVitals.js", [], [], "D:\\mysql-project\\frontend\\src\\components\\Register.js", [], [], "D:\\mysql-project\\frontend\\src\\contexts\\AuthContext.js", [], [], "D:\\mysql-project\\frontend\\src\\components\\Login.js", [], [], "D:\\mysql-project\\frontend\\src\\components\\PrivateRoute.js", [], [], "D:\\mysql-project\\frontend\\src\\components\\Dashboard.js", [], []]