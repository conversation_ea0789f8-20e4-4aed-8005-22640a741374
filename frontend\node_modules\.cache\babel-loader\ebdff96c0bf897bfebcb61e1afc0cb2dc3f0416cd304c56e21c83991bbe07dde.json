{"ast": null, "code": "var _jsxFileName = \"D:\\\\mysql-project\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\n// API base URL\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Auth context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Auth reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.REGISTER_START:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n    case AUTH_ACTIONS.REGISTER_SUCCESS:\n      localStorage.setItem('token', action.payload.token);\n      if (action.payload.refreshToken) {\n        localStorage.setItem('refreshToken', action.payload.refreshToken);\n      }\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        loading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.REGISTER_FAILURE:\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      return {\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: false,\n        error: null,\n        initialized: true\n      };\n    case AUTH_ACTIONS.LOAD_USER:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        loading: false,\n        initialized: true\n      };\n    case AUTH_ACTIONS.SET_INITIALIZED:\n      return {\n        ...state,\n        loading: false,\n        initialized: true\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n\n    // Response interceptor for handling token expiration\n    const responseInterceptor = api.interceptors.response.use(response => response, async error => {\n      var _error$response;\n      const currentToken = localStorage.getItem('token');\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && currentToken) {\n        // Token expired, try to refresh\n        try {\n          const refreshToken = localStorage.getItem('refreshToken');\n          if (refreshToken) {\n            const response = await api.post('/auth/refresh-token', {\n              refreshToken\n            });\n            const newToken = response.data.data.token;\n            localStorage.setItem('token', newToken);\n            api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n\n            // Retry original request\n            error.config.headers['Authorization'] = `Bearer ${newToken}`;\n            return api.request(error.config);\n          }\n        } catch (refreshError) {\n          // Refresh failed, logout user\n          dispatch({\n            type: AUTH_ACTIONS.LOGOUT\n          });\n        }\n      }\n      return Promise.reject(error);\n    });\n    return () => {\n      api.interceptors.response.eject(responseInterceptor);\n    };\n  }, []);\n\n  // Load user on app start - ONLY RUN ONCE\n  useEffect(() => {\n    let isMounted = true;\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n      if (token && isMounted) {\n        try {\n          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n          const response = await api.get('/auth/profile');\n          if (isMounted) {\n            dispatch({\n              type: AUTH_ACTIONS.LOAD_USER,\n              payload: response.data.data.user\n            });\n          }\n        } catch (error) {\n          console.error('Failed to load user:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          delete api.defaults.headers.common['Authorization'];\n          if (isMounted) {\n            dispatch({\n              type: AUTH_ACTIONS.SET_INITIALIZED\n            });\n          }\n        }\n      } else if (isMounted) {\n        dispatch({\n          type: AUTH_ACTIONS.SET_INITIALIZED\n        });\n      }\n    };\n    initializeAuth();\n    return () => {\n      isMounted = false;\n    };\n  }, []); // EMPTY DEPENDENCY ARRAY - ONLY RUN ONCE ON MOUNT\n\n  // Login function\n  const login = async (email, password) => {\n    dispatch({\n      type: AUTH_ACTIONS.LOGIN_START\n    });\n    try {\n      const response = await api.post('/auth/login', {\n        email,\n        password\n      });\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: response.data.data\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Register function\n  const register = async userData => {\n    dispatch({\n      type: AUTH_ACTIONS.REGISTER_START\n    });\n    try {\n      const response = await api.post('/auth/register', userData);\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS,\n        payload: response.data.data\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      dispatch({\n        type: AUTH_ACTIONS.LOGOUT\n      });\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 248,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"GUSXxL/WUElrtHc/X73NyHNRMdw=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "AuthContext", "authReducer", "state", "action", "type", "AUTH_ACTIONS", "LOGIN_START", "REGISTER_START", "loading", "error", "LOGIN_SUCCESS", "REGISTER_SUCCESS", "localStorage", "setItem", "payload", "token", "refreshToken", "user", "isAuthenticated", "LOGIN_FAILURE", "REGISTER_FAILURE", "removeItem", "LOGOUT", "initialized", "LOAD_USER", "SET_INITIALIZED", "CLEAR_ERROR", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "useReducer", "initialState", "getItem", "defaults", "common", "responseInterceptor", "interceptors", "response", "use", "_error$response", "currentToken", "status", "post", "newToken", "data", "config", "request", "refreshError", "Promise", "reject", "eject", "isMounted", "initializeAuth", "get", "console", "login", "email", "password", "success", "_error$response2", "_error$response2$data", "errorMessage", "message", "register", "userData", "_error$response3", "_error$response3$data", "logout", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/mysql-project/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\n// API base URL\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Auth context\nconst AuthContext = createContext();\n\n// Auth reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.REGISTER_START:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n    case AUTH_ACTIONS.REGISTER_SUCCESS:\n      localStorage.setItem('token', action.payload.token);\n      if (action.payload.refreshToken) {\n        localStorage.setItem('refreshToken', action.payload.refreshToken);\n      }\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        loading: false,\n        error: null\n      };\n\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.REGISTER_FAILURE:\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: false,\n        error: action.payload\n      };\n\n    case AUTH_ACTIONS.LOGOUT:\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      return {\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: false,\n        error: null,\n        initialized: true\n      };\n\n    case AUTH_ACTIONS.LOAD_USER:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        loading: false,\n        initialized: true\n      };\n\n    case AUTH_ACTIONS.SET_INITIALIZED:\n      return {\n        ...state,\n        loading: false,\n        initialized: true\n      };\n\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n\n    default:\n      return state;\n  }\n};\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n\n    // Response interceptor for handling token expiration\n    const responseInterceptor = api.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const currentToken = localStorage.getItem('token');\n        if (error.response?.status === 401 && currentToken) {\n          // Token expired, try to refresh\n          try {\n            const refreshToken = localStorage.getItem('refreshToken');\n            if (refreshToken) {\n              const response = await api.post('/auth/refresh-token', {\n                refreshToken\n              });\n\n              const newToken = response.data.data.token;\n              localStorage.setItem('token', newToken);\n              api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n\n              // Retry original request\n              error.config.headers['Authorization'] = `Bearer ${newToken}`;\n              return api.request(error.config);\n            }\n          } catch (refreshError) {\n            // Refresh failed, logout user\n            dispatch({ type: AUTH_ACTIONS.LOGOUT });\n          }\n        }\n        return Promise.reject(error);\n      }\n    );\n\n    return () => {\n      api.interceptors.response.eject(responseInterceptor);\n    };\n  }, []);\n\n  // Load user on app start - ONLY RUN ONCE\n  useEffect(() => {\n    let isMounted = true;\n\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n\n      if (token && isMounted) {\n        try {\n          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n          const response = await api.get('/auth/profile');\n\n          if (isMounted) {\n            dispatch({\n              type: AUTH_ACTIONS.LOAD_USER,\n              payload: response.data.data.user\n            });\n          }\n        } catch (error) {\n          console.error('Failed to load user:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          delete api.defaults.headers.common['Authorization'];\n\n          if (isMounted) {\n            dispatch({ type: AUTH_ACTIONS.SET_INITIALIZED });\n          }\n        }\n      } else if (isMounted) {\n        dispatch({ type: AUTH_ACTIONS.SET_INITIALIZED });\n      }\n    };\n\n    initializeAuth();\n\n    return () => {\n      isMounted = false;\n    };\n  }, []); // EMPTY DEPENDENCY ARRAY - ONLY RUN ONCE ON MOUNT\n\n  // Login function\n  const login = async (email, password) => {\n    dispatch({ type: AUTH_ACTIONS.LOGIN_START });\n    try {\n      const response = await api.post('/auth/login', { email, password });\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: response.data.data\n      });\n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Register function\n  const register = async (userData) => {\n    dispatch({ type: AUTH_ACTIONS.REGISTER_START });\n    try {\n      const response = await api.post('/auth/register', userData);\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS,\n        payload: response.data.data\n      });\n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: errorMessage\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      dispatch({ type: AUTH_ACTIONS.LOGOUT });\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    clearError\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,WAAW,gBAAGf,aAAa,CAAC,CAAC;;AAEnC;AACA,MAAMgB,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKC,YAAY,CAACC,WAAW;IAC7B,KAAKD,YAAY,CAACE,cAAc;MAC9B,OAAO;QACL,GAAGL,KAAK;QACRM,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKJ,YAAY,CAACK,aAAa;IAC/B,KAAKL,YAAY,CAACM,gBAAgB;MAChCC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEV,MAAM,CAACW,OAAO,CAACC,KAAK,CAAC;MACnD,IAAIZ,MAAM,CAACW,OAAO,CAACE,YAAY,EAAE;QAC/BJ,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEV,MAAM,CAACW,OAAO,CAACE,YAAY,CAAC;MACnE;MACA,OAAO;QACL,GAAGd,KAAK;QACRe,IAAI,EAAEd,MAAM,CAACW,OAAO,CAACG,IAAI;QACzBF,KAAK,EAAEZ,MAAM,CAACW,OAAO,CAACC,KAAK;QAC3BG,eAAe,EAAE,IAAI;QACrBV,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKJ,YAAY,CAACc,aAAa;IAC/B,KAAKd,YAAY,CAACe,gBAAgB;MAChCR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;MAChCT,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;MACvC,OAAO;QACL,GAAGnB,KAAK;QACRe,IAAI,EAAE,IAAI;QACVF,KAAK,EAAE,IAAI;QACXG,eAAe,EAAE,KAAK;QACtBV,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEN,MAAM,CAACW;MAChB,CAAC;IAEH,KAAKT,YAAY,CAACiB,MAAM;MACtBV,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;MAChCT,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;MACvC,OAAO;QACLJ,IAAI,EAAE,IAAI;QACVF,KAAK,EAAE,IAAI;QACXG,eAAe,EAAE,KAAK;QACtBV,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE,IAAI;QACXc,WAAW,EAAE;MACf,CAAC;IAEH,KAAKlB,YAAY,CAACmB,SAAS;MACzB,OAAO;QACL,GAAGtB,KAAK;QACRe,IAAI,EAAEd,MAAM,CAACW,OAAO;QACpBI,eAAe,EAAE,IAAI;QACrBV,OAAO,EAAE,KAAK;QACde,WAAW,EAAE;MACf,CAAC;IAEH,KAAKlB,YAAY,CAACoB,eAAe;MAC/B,OAAO;QACL,GAAGvB,KAAK;QACRM,OAAO,EAAE,KAAK;QACde,WAAW,EAAE;MACf,CAAC;IAEH,KAAKlB,YAAY,CAACqB,WAAW;MAC3B,OAAO;QACL,GAAGxB,KAAK;QACRO,KAAK,EAAE;MACT,CAAC;IAEH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;;AAED;AACA,OAAO,MAAMyB,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAAC3B,KAAK,EAAE4B,QAAQ,CAAC,GAAGC,UAAU,CAAC9B,WAAW,EAAE+B,YAAY,CAAC;;EAE/D;EACA5C,SAAS,CAAC,MAAM;IACd,MAAM2B,KAAK,GAAGH,YAAY,CAACqB,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIlB,KAAK,EAAE;MACTnB,GAAG,CAACsC,QAAQ,CAACnC,OAAO,CAACoC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUpB,KAAK,EAAE;IAClE;;IAEA;IACA,MAAMqB,mBAAmB,GAAGxC,GAAG,CAACyC,YAAY,CAACC,QAAQ,CAACC,GAAG,CACtDD,QAAQ,IAAKA,QAAQ,EACtB,MAAO7B,KAAK,IAAK;MAAA,IAAA+B,eAAA;MACf,MAAMC,YAAY,GAAG7B,YAAY,CAACqB,OAAO,CAAC,OAAO,CAAC;MAClD,IAAI,EAAAO,eAAA,GAAA/B,KAAK,CAAC6B,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAID,YAAY,EAAE;QAClD;QACA,IAAI;UACF,MAAMzB,YAAY,GAAGJ,YAAY,CAACqB,OAAO,CAAC,cAAc,CAAC;UACzD,IAAIjB,YAAY,EAAE;YAChB,MAAMsB,QAAQ,GAAG,MAAM1C,GAAG,CAAC+C,IAAI,CAAC,qBAAqB,EAAE;cACrD3B;YACF,CAAC,CAAC;YAEF,MAAM4B,QAAQ,GAAGN,QAAQ,CAACO,IAAI,CAACA,IAAI,CAAC9B,KAAK;YACzCH,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE+B,QAAQ,CAAC;YACvChD,GAAG,CAACsC,QAAQ,CAACnC,OAAO,CAACoC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUS,QAAQ,EAAE;;YAEnE;YACAnC,KAAK,CAACqC,MAAM,CAAC/C,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU6C,QAAQ,EAAE;YAC5D,OAAOhD,GAAG,CAACmD,OAAO,CAACtC,KAAK,CAACqC,MAAM,CAAC;UAClC;QACF,CAAC,CAAC,OAAOE,YAAY,EAAE;UACrB;UACAlB,QAAQ,CAAC;YAAE1B,IAAI,EAAEC,YAAY,CAACiB;UAAO,CAAC,CAAC;QACzC;MACF;MACA,OAAO2B,OAAO,CAACC,MAAM,CAACzC,KAAK,CAAC;IAC9B,CACF,CAAC;IAED,OAAO,MAAM;MACXb,GAAG,CAACyC,YAAY,CAACC,QAAQ,CAACa,KAAK,CAACf,mBAAmB,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhD,SAAS,CAAC,MAAM;IACd,IAAIgE,SAAS,GAAG,IAAI;IAEpB,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAMtC,KAAK,GAAGH,YAAY,CAACqB,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAIlB,KAAK,IAAIqC,SAAS,EAAE;QACtB,IAAI;UACFxD,GAAG,CAACsC,QAAQ,CAACnC,OAAO,CAACoC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUpB,KAAK,EAAE;UAChE,MAAMuB,QAAQ,GAAG,MAAM1C,GAAG,CAAC0D,GAAG,CAAC,eAAe,CAAC;UAE/C,IAAIF,SAAS,EAAE;YACbtB,QAAQ,CAAC;cACP1B,IAAI,EAAEC,YAAY,CAACmB,SAAS;cAC5BV,OAAO,EAAEwB,QAAQ,CAACO,IAAI,CAACA,IAAI,CAAC5B;YAC9B,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAOR,KAAK,EAAE;UACd8C,OAAO,CAAC9C,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5CG,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;UAChCT,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;UACvC,OAAOzB,GAAG,CAACsC,QAAQ,CAACnC,OAAO,CAACoC,MAAM,CAAC,eAAe,CAAC;UAEnD,IAAIiB,SAAS,EAAE;YACbtB,QAAQ,CAAC;cAAE1B,IAAI,EAAEC,YAAY,CAACoB;YAAgB,CAAC,CAAC;UAClD;QACF;MACF,CAAC,MAAM,IAAI2B,SAAS,EAAE;QACpBtB,QAAQ,CAAC;UAAE1B,IAAI,EAAEC,YAAY,CAACoB;QAAgB,CAAC,CAAC;MAClD;IACF,CAAC;IAED4B,cAAc,CAAC,CAAC;IAEhB,OAAO,MAAM;MACXD,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMI,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC5B,QAAQ,CAAC;MAAE1B,IAAI,EAAEC,YAAY,CAACC;IAAY,CAAC,CAAC;IAC5C,IAAI;MACF,MAAMgC,QAAQ,GAAG,MAAM1C,GAAG,CAAC+C,IAAI,CAAC,aAAa,EAAE;QAAEc,KAAK;QAAEC;MAAS,CAAC,CAAC;MACnE5B,QAAQ,CAAC;QACP1B,IAAI,EAAEC,YAAY,CAACK,aAAa;QAChCI,OAAO,EAAEwB,QAAQ,CAACO,IAAI,CAACA;MACzB,CAAC,CAAC;MACF,OAAO;QAAEc,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOlD,KAAK,EAAE;MAAA,IAAAmD,gBAAA,EAAAC,qBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,gBAAA,GAAAnD,KAAK,CAAC6B,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAAI,cAAc;MACpEjC,QAAQ,CAAC;QACP1B,IAAI,EAAEC,YAAY,CAACc,aAAa;QAChCL,OAAO,EAAEgD;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElD,KAAK,EAAEqD;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAME,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnCnC,QAAQ,CAAC;MAAE1B,IAAI,EAAEC,YAAY,CAACE;IAAe,CAAC,CAAC;IAC/C,IAAI;MACF,MAAM+B,QAAQ,GAAG,MAAM1C,GAAG,CAAC+C,IAAI,CAAC,gBAAgB,EAAEsB,QAAQ,CAAC;MAC3DnC,QAAQ,CAAC;QACP1B,IAAI,EAAEC,YAAY,CAACM,gBAAgB;QACnCG,OAAO,EAAEwB,QAAQ,CAACO,IAAI,CAACA;MACzB,CAAC,CAAC;MACF,OAAO;QAAEc,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOlD,KAAK,EAAE;MAAA,IAAAyD,gBAAA,EAAAC,qBAAA;MACd,MAAML,YAAY,GAAG,EAAAI,gBAAA,GAAAzD,KAAK,CAAC6B,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,qBAAqB;MAC3EjC,QAAQ,CAAC;QACP1B,IAAI,EAAEC,YAAY,CAACe,gBAAgB;QACnCN,OAAO,EAAEgD;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElD,KAAK,EAAEqD;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMM,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMxE,GAAG,CAAC+C,IAAI,CAAC,cAAc,CAAC;IAChC,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACd8C,OAAO,CAAC9C,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRqB,QAAQ,CAAC;QAAE1B,IAAI,EAAEC,YAAY,CAACiB;MAAO,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAM+C,UAAU,GAAGA,CAAA,KAAM;IACvBvC,QAAQ,CAAC;MAAE1B,IAAI,EAAEC,YAAY,CAACqB;IAAY,CAAC,CAAC;EAC9C,CAAC;EAED,MAAM4C,KAAK,GAAG;IACZ,GAAGpE,KAAK;IACRsD,KAAK;IACLQ,QAAQ;IACRI,MAAM;IACNC;EACF,CAAC;EAED,oBACE9E,OAAA,CAACS,WAAW,CAACuE,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA1C,QAAA,EAChCA;EAAQ;IAAA4C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAA9C,EAAA,CA7JaF,YAAY;AAAAiD,EAAA,GAAZjD,YAAY;AA8JzB,OAAO,MAAMkD,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG7F,UAAU,CAACc,WAAW,CAAC;EACvC,IAAI,CAAC+E,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAe7E,WAAW;AAAC,IAAA4E,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}