# MySQL Authentication Project

A complete full-stack authentication system built with **React**, **Node.js**, **Express**, and **MySQL**. This project demonstrates secure user authentication with JWT tokens, password hashing, and a modern responsive UI.

## 🚀 Features

- **User Registration** with validation
- **User Login** with secure authentication
- **JWT Token-based** authentication
- **Password hashing** with bcrypt
- **Protected routes** and middleware
- **Responsive design** with modern UI
- **MySQL database** integration
- **Session management**
- **Error handling** and validation
- **Security best practices**

## 🛠️ Tech Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MySQL** - Database
- **JWT** - Authentication tokens
- **bcryptjs** - Password hashing
- **express-validator** - Input validation
- **helmet** - Security middleware
- **cors** - Cross-origin resource sharing
- **express-rate-limit** - Rate limiting

### Frontend
- **React** - UI library
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **Context API** - State management
- **CSS3** - Styling with modern design

## 📋 Prerequisites

Before running this project, make sure you have the following installed:

- **Node.js** (v14 or higher)
- **npm** or **yarn**
- **MySQL** (v5.7 or higher)
- **Git**

## 🔧 Installation & Setup

### 1. Clone the Repository
```bash
git clone <your-repo-url>
cd mysql-project
```

### 2. Install Dependencies
```bash
# Install root dependencies
npm install

# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

### 3. Database Setup

#### Option A: Using MySQL Command Line
```sql
-- Connect to MySQL
mysql -u root -p

-- Create database
CREATE DATABASE auth_db;

-- Create user (optional)
CREATE USER 'auth_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON auth_db.* TO 'auth_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Option B: Using MySQL Workbench
1. Open MySQL Workbench
2. Connect to your MySQL server
3. Create a new schema named `auth_db`

### 4. Environment Configuration

Copy the environment example file and configure your settings:

```bash
cd backend
cp .env.example .env
```

Edit the `.env` file with your database credentials:

```env
# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=auth_db
DB_PORT=3306

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_EXPIRE=7d

# Server Configuration
PORT=5000
NODE_ENV=development

# CORS Configuration
CLIENT_URL=http://localhost:3000
```

### 5. Initialize Database Tables

Run the database setup script:

```bash
cd backend
node scripts/setup-database.js
```

This will:
- Create the database if it doesn't exist
- Create the users table with proper schema
- Set up indexes for performance

## 🚀 Running the Application

### Development Mode

#### Option 1: Run Both Frontend and Backend Together
```bash
# From the root directory
npm run dev
```

#### Option 2: Run Separately

**Backend (Terminal 1):**
```bash
cd backend
npm run dev
```

**Frontend (Terminal 2):**
```bash
cd frontend
npm start
```

### Production Mode

```bash
# Build frontend
cd frontend
npm run build

# Start backend
cd ../backend
npm start
```

## 📱 Usage

1. **Access the Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

2. **Register a New Account**
   - Go to http://localhost:3000/register
   - Fill in the registration form
   - Submit to create your account

3. **Login**
   - Go to http://localhost:3000/login
   - Enter your email and password
   - Successfully login to access the dashboard

4. **Dashboard**
   - View your profile information
   - See account status
   - Access quick actions
   - Logout when done

## 🔐 API Endpoints

### Authentication Routes

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/auth/register` | Register new user | No |
| POST | `/api/auth/login` | Login user | No |
| GET | `/api/auth/profile` | Get user profile | Yes |
| POST | `/api/auth/logout` | Logout user | Yes |
| POST | `/api/auth/refresh-token` | Refresh JWT token | No |
| GET | `/api/auth/health` | Health check | No |

### Example API Usage

**Register User:**
```bash
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "johndoe",
    "email": "<EMAIL>",
    "password": "Password123",
    "first_name": "John",
    "last_name": "Doe"
  }'
```

**Login User:**
```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123"
  }'
```

**Get Profile (with token):**
```bash
curl -X GET http://localhost:5000/api/auth/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🗄️ Database Schema

### Users Table
```sql
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(50),
  last_name VARCHAR(50),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  last_login TIMESTAMP NULL,
  INDEX idx_username (username),
  INDEX idx_email (email)
);
```

## 🔒 Security Features

- **Password Hashing**: bcrypt with salt rounds
- **JWT Tokens**: Secure token-based authentication
- **Input Validation**: Server-side validation with express-validator
- **Rate Limiting**: Protection against brute force attacks
- **CORS Configuration**: Controlled cross-origin requests
- **Helmet**: Security headers
- **SQL Injection Protection**: Parameterized queries
- **XSS Protection**: Input sanitization

## 🧪 Testing

### Manual Testing Checklist

- [ ] User can register with valid data
- [ ] Registration fails with invalid data
- [ ] User can login with correct credentials
- [ ] Login fails with incorrect credentials
- [ ] Dashboard shows user information
- [ ] Protected routes require authentication
- [ ] User can logout successfully
- [ ] Token refresh works correctly

### API Testing with curl

Test the API endpoints using the curl examples provided above.

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check MySQL is running
   - Verify database credentials in `.env`
   - Ensure database exists

2. **Port Already in Use**
   - Change PORT in `.env` file
   - Kill existing processes on ports 3000/5000

3. **JWT Token Issues**
   - Ensure JWT_SECRET is set in `.env`
   - Check token expiration settings

4. **CORS Errors**
   - Verify CLIENT_URL in backend `.env`
   - Check frontend API base URL

### Logs and Debugging

- Backend logs: Check terminal running `npm run dev`
- Frontend logs: Check browser console
- Database logs: Check MySQL error logs

## 📝 Project Structure

```
mysql-project/
├── backend/
│   ├── config/
│   │   └── database.js
│   ├── controllers/
│   │   └── authController.js
│   ├── middleware/
│   │   ├── auth.js
│   │   └── validation.js
│   ├── models/
│   │   └── User.js
│   ├── routes/
│   │   └── auth.js
│   ├── scripts/
│   │   └── setup-database.js
│   ├── utils/
│   │   └── jwt.js
│   ├── .env
│   ├── .env.example
│   ├── package.json
│   └── server.js
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   │   ├── Auth.css
│   │   │   ├── Dashboard.css
│   │   │   ├── Dashboard.js
│   │   │   ├── Login.js
│   │   │   ├── PrivateRoute.js
│   │   │   └── Register.js
│   │   ├── contexts/
│   │   │   └── AuthContext.js
│   │   ├── App.css
│   │   ├── App.js
│   │   └── index.js
│   └── package.json
├── package.json
└── README.md
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 👨‍💻 Author

Created as a demonstration of full-stack authentication with modern web technologies.

---

**Happy Coding! 🎉**
