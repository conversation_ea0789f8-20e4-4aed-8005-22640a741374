{"ast": null, "code": "var _jsxFileName = \"D:\\\\mysql-project\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\n// API base URL\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Auth context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n\n    // Response interceptor for handling token expiration\n    const responseInterceptor = api.interceptors.response.use(response => response, async error => {\n      var _error$response;\n      const currentToken = localStorage.getItem('token');\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && currentToken) {\n        // Token expired, try to refresh\n        try {\n          const refreshToken = localStorage.getItem('refreshToken');\n          if (refreshToken) {\n            const response = await api.post('/auth/refresh-token', {\n              refreshToken\n            });\n            const newToken = response.data.data.token;\n            localStorage.setItem('token', newToken);\n            api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n\n            // Retry original request\n            error.config.headers['Authorization'] = `Bearer ${newToken}`;\n            return api.request(error.config);\n          }\n        } catch (refreshError) {\n          // Refresh failed, logout user\n          dispatch({\n            type: AUTH_ACTIONS.LOGOUT\n          });\n        }\n      }\n      return Promise.reject(error);\n    });\n    return () => {\n      api.interceptors.response.eject(responseInterceptor);\n    };\n  }, []);\n\n  // Load user on app start - ONLY RUN ONCE\n  useEffect(() => {\n    let isMounted = true;\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n      if (token && isMounted) {\n        try {\n          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n          const response = await api.get('/auth/profile');\n          if (isMounted) {\n            dispatch({\n              type: AUTH_ACTIONS.LOAD_USER,\n              payload: response.data.data.user\n            });\n          }\n        } catch (error) {\n          console.error('Failed to load user:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          delete api.defaults.headers.common['Authorization'];\n          if (isMounted) {\n            dispatch({\n              type: AUTH_ACTIONS.SET_INITIALIZED\n            });\n          }\n        }\n      } else if (isMounted) {\n        dispatch({\n          type: AUTH_ACTIONS.SET_INITIALIZED\n        });\n      }\n    };\n    initializeAuth();\n    return () => {\n      isMounted = false;\n    };\n  }, []); // EMPTY DEPENDENCY ARRAY - ONLY RUN ONCE ON MOUNT\n\n  // Login function\n  const login = async (email, password) => {\n    dispatch({\n      type: AUTH_ACTIONS.LOGIN_START\n    });\n    try {\n      const response = await api.post('/auth/login', {\n        email,\n        password\n      });\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: response.data.data\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Register function\n  const register = async userData => {\n    dispatch({\n      type: AUTH_ACTIONS.REGISTER_START\n    });\n    try {\n      const response = await api.post('/auth/register', userData);\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS,\n        payload: response.data.data\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      dispatch({\n        type: AUTH_ACTIONS.LOGOUT\n      });\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"iLpaRsBcvK5kw9CoUYngETMeKsY=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "token", "setToken", "localStorage", "getItem", "loading", "setLoading", "error", "setError", "defaults", "common", "responseInterceptor", "interceptors", "response", "use", "_error$response", "currentToken", "status", "refreshToken", "post", "newToken", "data", "setItem", "config", "request", "refreshError", "dispatch", "type", "AUTH_ACTIONS", "LOGOUT", "Promise", "reject", "eject", "isMounted", "initializeAuth", "get", "LOAD_USER", "payload", "console", "removeItem", "SET_INITIALIZED", "login", "email", "password", "LOGIN_START", "LOGIN_SUCCESS", "success", "_error$response2", "_error$response2$data", "errorMessage", "message", "LOGIN_FAILURE", "register", "userData", "REGISTER_START", "REGISTER_SUCCESS", "_error$response3", "_error$response3$data", "REGISTER_FAILURE", "logout", "clearError", "CLEAR_ERROR", "value", "state", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/mysql-project/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\n// API base URL\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Auth context\nconst AuthContext = createContext();\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n\n    // Response interceptor for handling token expiration\n    const responseInterceptor = api.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const currentToken = localStorage.getItem('token');\n        if (error.response?.status === 401 && currentToken) {\n          // Token expired, try to refresh\n          try {\n            const refreshToken = localStorage.getItem('refreshToken');\n            if (refreshToken) {\n              const response = await api.post('/auth/refresh-token', {\n                refreshToken\n              });\n\n              const newToken = response.data.data.token;\n              localStorage.setItem('token', newToken);\n              api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n\n              // Retry original request\n              error.config.headers['Authorization'] = `Bearer ${newToken}`;\n              return api.request(error.config);\n            }\n          } catch (refreshError) {\n            // Refresh failed, logout user\n            dispatch({ type: AUTH_ACTIONS.LOGOUT });\n          }\n        }\n        return Promise.reject(error);\n      }\n    );\n\n    return () => {\n      api.interceptors.response.eject(responseInterceptor);\n    };\n  }, []);\n\n  // Load user on app start - ONLY RUN ONCE\n  useEffect(() => {\n    let isMounted = true;\n\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n\n      if (token && isMounted) {\n        try {\n          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n          const response = await api.get('/auth/profile');\n\n          if (isMounted) {\n            dispatch({\n              type: AUTH_ACTIONS.LOAD_USER,\n              payload: response.data.data.user\n            });\n          }\n        } catch (error) {\n          console.error('Failed to load user:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          delete api.defaults.headers.common['Authorization'];\n\n          if (isMounted) {\n            dispatch({ type: AUTH_ACTIONS.SET_INITIALIZED });\n          }\n        }\n      } else if (isMounted) {\n        dispatch({ type: AUTH_ACTIONS.SET_INITIALIZED });\n      }\n    };\n\n    initializeAuth();\n\n    return () => {\n      isMounted = false;\n    };\n  }, []); // EMPTY DEPENDENCY ARRAY - ONLY RUN ONCE ON MOUNT\n\n  // Login function\n  const login = async (email, password) => {\n    dispatch({ type: AUTH_ACTIONS.LOGIN_START });\n    try {\n      const response = await api.post('/auth/login', { email, password });\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: response.data.data\n      });\n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Register function\n  const register = async (userData) => {\n    dispatch({ type: AUTH_ACTIONS.REGISTER_START });\n    try {\n      const response = await api.post('/auth/register', userData);\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS,\n        payload: response.data.data\n      });\n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: errorMessage\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      dispatch({ type: AUTH_ACTIONS.LOGOUT });\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    clearError\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,WAAW,gBAAGf,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAMgB,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAACqB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;EACjE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,KAAK,GAAGE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIH,KAAK,EAAE;MACTV,GAAG,CAACkB,QAAQ,CAACf,OAAO,CAACgB,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUT,KAAK,EAAE;IAClE;;IAEA;IACA,MAAMU,mBAAmB,GAAGpB,GAAG,CAACqB,YAAY,CAACC,QAAQ,CAACC,GAAG,CACtDD,QAAQ,IAAKA,QAAQ,EACtB,MAAON,KAAK,IAAK;MAAA,IAAAQ,eAAA;MACf,MAAMC,YAAY,GAAGb,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAClD,IAAI,EAAAW,eAAA,GAAAR,KAAK,CAACM,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAID,YAAY,EAAE;QAClD;QACA,IAAI;UACF,MAAME,YAAY,GAAGf,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;UACzD,IAAIc,YAAY,EAAE;YAChB,MAAML,QAAQ,GAAG,MAAMtB,GAAG,CAAC4B,IAAI,CAAC,qBAAqB,EAAE;cACrDD;YACF,CAAC,CAAC;YAEF,MAAME,QAAQ,GAAGP,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAACpB,KAAK;YACzCE,YAAY,CAACmB,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC;YACvC7B,GAAG,CAACkB,QAAQ,CAACf,OAAO,CAACgB,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUU,QAAQ,EAAE;;YAEnE;YACAb,KAAK,CAACgB,MAAM,CAAC7B,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU0B,QAAQ,EAAE;YAC5D,OAAO7B,GAAG,CAACiC,OAAO,CAACjB,KAAK,CAACgB,MAAM,CAAC;UAClC;QACF,CAAC,CAAC,OAAOE,YAAY,EAAE;UACrB;UACAC,QAAQ,CAAC;YAAEC,IAAI,EAAEC,YAAY,CAACC;UAAO,CAAC,CAAC;QACzC;MACF;MACA,OAAOC,OAAO,CAACC,MAAM,CAACxB,KAAK,CAAC;IAC9B,CACF,CAAC;IAED,OAAO,MAAM;MACXhB,GAAG,CAACqB,YAAY,CAACC,QAAQ,CAACmB,KAAK,CAACrB,mBAAmB,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5B,SAAS,CAAC,MAAM;IACd,IAAIkD,SAAS,GAAG,IAAI;IAEpB,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAMjC,KAAK,GAAGE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAIH,KAAK,IAAIgC,SAAS,EAAE;QACtB,IAAI;UACF1C,GAAG,CAACkB,QAAQ,CAACf,OAAO,CAACgB,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUT,KAAK,EAAE;UAChE,MAAMY,QAAQ,GAAG,MAAMtB,GAAG,CAAC4C,GAAG,CAAC,eAAe,CAAC;UAE/C,IAAIF,SAAS,EAAE;YACbP,QAAQ,CAAC;cACPC,IAAI,EAAEC,YAAY,CAACQ,SAAS;cAC5BC,OAAO,EAAExB,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAACtB;YAC9B,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;UACd+B,OAAO,CAAC/B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5CJ,YAAY,CAACoC,UAAU,CAAC,OAAO,CAAC;UAChCpC,YAAY,CAACoC,UAAU,CAAC,cAAc,CAAC;UACvC,OAAOhD,GAAG,CAACkB,QAAQ,CAACf,OAAO,CAACgB,MAAM,CAAC,eAAe,CAAC;UAEnD,IAAIuB,SAAS,EAAE;YACbP,QAAQ,CAAC;cAAEC,IAAI,EAAEC,YAAY,CAACY;YAAgB,CAAC,CAAC;UAClD;QACF;MACF,CAAC,MAAM,IAAIP,SAAS,EAAE;QACpBP,QAAQ,CAAC;UAAEC,IAAI,EAAEC,YAAY,CAACY;QAAgB,CAAC,CAAC;MAClD;IACF,CAAC;IAEDN,cAAc,CAAC,CAAC;IAEhB,OAAO,MAAM;MACXD,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMQ,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvCjB,QAAQ,CAAC;MAAEC,IAAI,EAAEC,YAAY,CAACgB;IAAY,CAAC,CAAC;IAC5C,IAAI;MACF,MAAM/B,QAAQ,GAAG,MAAMtB,GAAG,CAAC4B,IAAI,CAAC,aAAa,EAAE;QAAEuB,KAAK;QAAEC;MAAS,CAAC,CAAC;MACnEjB,QAAQ,CAAC;QACPC,IAAI,EAAEC,YAAY,CAACiB,aAAa;QAChCR,OAAO,EAAExB,QAAQ,CAACQ,IAAI,CAACA;MACzB,CAAC,CAAC;MACF,OAAO;QAAEyB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOvC,KAAK,EAAE;MAAA,IAAAwC,gBAAA,EAAAC,qBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,gBAAA,GAAAxC,KAAK,CAACM,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAAI,cAAc;MACpExB,QAAQ,CAAC;QACPC,IAAI,EAAEC,YAAY,CAACuB,aAAa;QAChCd,OAAO,EAAEY;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEvC,KAAK,EAAE0C;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMG,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC3B,QAAQ,CAAC;MAAEC,IAAI,EAAEC,YAAY,CAAC0B;IAAe,CAAC,CAAC;IAC/C,IAAI;MACF,MAAMzC,QAAQ,GAAG,MAAMtB,GAAG,CAAC4B,IAAI,CAAC,gBAAgB,EAAEkC,QAAQ,CAAC;MAC3D3B,QAAQ,CAAC;QACPC,IAAI,EAAEC,YAAY,CAAC2B,gBAAgB;QACnClB,OAAO,EAAExB,QAAQ,CAACQ,IAAI,CAACA;MACzB,CAAC,CAAC;MACF,OAAO;QAAEyB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOvC,KAAK,EAAE;MAAA,IAAAiD,gBAAA,EAAAC,qBAAA;MACd,MAAMR,YAAY,GAAG,EAAAO,gBAAA,GAAAjD,KAAK,CAACM,QAAQ,cAAA2C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnC,IAAI,cAAAoC,qBAAA,uBAApBA,qBAAA,CAAsBP,OAAO,KAAI,qBAAqB;MAC3ExB,QAAQ,CAAC;QACPC,IAAI,EAAEC,YAAY,CAAC8B,gBAAgB;QACnCrB,OAAO,EAAEY;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEvC,KAAK,EAAE0C;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMU,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMpE,GAAG,CAAC4B,IAAI,CAAC,cAAc,CAAC;IAChC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd+B,OAAO,CAAC/B,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRmB,QAAQ,CAAC;QAAEC,IAAI,EAAEC,YAAY,CAACC;MAAO,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAM+B,UAAU,GAAGA,CAAA,KAAM;IACvBlC,QAAQ,CAAC;MAAEC,IAAI,EAAEC,YAAY,CAACiC;IAAY,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMC,KAAK,GAAG;IACZ,GAAGC,KAAK;IACRtB,KAAK;IACLW,QAAQ;IACRO,MAAM;IACNC;EACF,CAAC;EAED,oBACE1E,OAAA,CAACS,WAAW,CAACqE,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAjE,QAAA,EAChCA;EAAQ;IAAAoE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAtE,EAAA,CAhKaF,YAAY;AAAAyE,EAAA,GAAZzE,YAAY;AAiKzB,OAAO,MAAM0E,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG3F,UAAU,CAACc,WAAW,CAAC;EACvC,IAAI,CAAC6E,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAe3E,WAAW;AAAC,IAAA0E,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}