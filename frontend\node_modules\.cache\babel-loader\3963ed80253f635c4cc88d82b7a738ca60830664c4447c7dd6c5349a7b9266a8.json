{"ast": null, "code": "var _jsxFileName = \"D:\\\\mysql-project\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport axios from 'axios';\n\n// API base URL\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Auth context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Auth actions\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  REGISTER_START: 'REGISTER_START',\n  REGISTER_SUCCESS: 'REGISTER_SUCCESS',\n  REGISTER_FAILURE: 'REGISTER_FAILURE',\n  LOAD_USER: 'LOAD_USER',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  SET_INITIALIZED: 'SET_INITIALIZED'\n};\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isAuthenticated: !!localStorage.getItem('token'),\n  loading: true,\n  error: null,\n  initialized: false\n};\n\n// Auth reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.REGISTER_START:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n    case AUTH_ACTIONS.REGISTER_SUCCESS:\n      localStorage.setItem('token', action.payload.token);\n      if (action.payload.refreshToken) {\n        localStorage.setItem('refreshToken', action.payload.refreshToken);\n      }\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        loading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.REGISTER_FAILURE:\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      return {\n        ...initialState,\n        loading: false\n      };\n    case AUTH_ACTIONS.LOAD_USER:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        loading: false,\n        initialized: true\n      };\n    case AUTH_ACTIONS.SET_INITIALIZED:\n      return {\n        ...state,\n        loading: false,\n        initialized: true\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n\n    // Response interceptor for handling token expiration\n    const responseInterceptor = api.interceptors.response.use(response => response, async error => {\n      var _error$response;\n      const currentToken = localStorage.getItem('token');\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && currentToken) {\n        // Token expired, try to refresh\n        try {\n          const refreshToken = localStorage.getItem('refreshToken');\n          if (refreshToken) {\n            const response = await api.post('/auth/refresh-token', {\n              refreshToken\n            });\n            const newToken = response.data.data.token;\n            localStorage.setItem('token', newToken);\n            api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n\n            // Retry original request\n            error.config.headers['Authorization'] = `Bearer ${newToken}`;\n            return api.request(error.config);\n          }\n        } catch (refreshError) {\n          // Refresh failed, logout user\n          dispatch({\n            type: AUTH_ACTIONS.LOGOUT\n          });\n        }\n      }\n      return Promise.reject(error);\n    });\n    return () => {\n      api.interceptors.response.eject(responseInterceptor);\n    };\n  }, []);\n\n  // Load user on app start - ONLY RUN ONCE\n  useEffect(() => {\n    let isMounted = true;\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n      if (token && isMounted) {\n        try {\n          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n          const response = await api.get('/auth/profile');\n          if (isMounted) {\n            dispatch({\n              type: AUTH_ACTIONS.LOAD_USER,\n              payload: response.data.data.user\n            });\n          }\n        } catch (error) {\n          console.error('Failed to load user:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          delete api.defaults.headers.common['Authorization'];\n          if (isMounted) {\n            dispatch({\n              type: AUTH_ACTIONS.SET_INITIALIZED\n            });\n          }\n        }\n      } else if (isMounted) {\n        dispatch({\n          type: AUTH_ACTIONS.SET_INITIALIZED\n        });\n      }\n    };\n    initializeAuth();\n    return () => {\n      isMounted = false;\n    };\n  }, []); // EMPTY DEPENDENCY ARRAY - ONLY RUN ONCE ON MOUNT\n\n  // Login function\n  const login = async (email, password) => {\n    dispatch({\n      type: AUTH_ACTIONS.LOGIN_START\n    });\n    try {\n      const response = await api.post('/auth/login', {\n        email,\n        password\n      });\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: response.data.data\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Register function\n  const register = async userData => {\n    dispatch({\n      type: AUTH_ACTIONS.REGISTER_START\n    });\n    try {\n      const response = await api.post('/auth/register', userData);\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS,\n        payload: response.data.data\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      dispatch({\n        type: AUTH_ACTIONS.LOGOUT\n      });\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 268,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"GUSXxL/WUElrtHc/X73NyHNRMdw=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "axios", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "AuthContext", "AUTH_ACTIONS", "LOGIN_START", "LOGIN_SUCCESS", "LOGIN_FAILURE", "LOGOUT", "REGISTER_START", "REGISTER_SUCCESS", "REGISTER_FAILURE", "LOAD_USER", "CLEAR_ERROR", "SET_INITIALIZED", "initialState", "user", "token", "localStorage", "getItem", "isAuthenticated", "loading", "error", "initialized", "authReducer", "state", "action", "type", "setItem", "payload", "refreshToken", "removeItem", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "defaults", "common", "responseInterceptor", "interceptors", "response", "use", "_error$response", "currentToken", "status", "post", "newToken", "data", "config", "request", "refreshError", "Promise", "reject", "eject", "isMounted", "initializeAuth", "get", "console", "login", "email", "password", "success", "_error$response2", "_error$response2$data", "errorMessage", "message", "register", "userData", "_error$response3", "_error$response3$data", "logout", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/mysql-project/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport axios from 'axios';\n\n// API base URL\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Auth context\nconst AuthContext = createContext();\n\n// Auth actions\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  REGISTER_START: 'REGISTER_START',\n  REGISTER_SUCCESS: 'REGISTER_SUCCESS',\n  REGISTER_FAILURE: 'REGISTER_FAILURE',\n  LOAD_USER: 'LOAD_USER',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  SET_INITIALIZED: 'SET_INITIALIZED'\n};\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isAuthenticated: !!localStorage.getItem('token'),\n  loading: true,\n  error: null,\n  initialized: false\n};\n\n// Auth reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.REGISTER_START:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n    case AUTH_ACTIONS.REGISTER_SUCCESS:\n      localStorage.setItem('token', action.payload.token);\n      if (action.payload.refreshToken) {\n        localStorage.setItem('refreshToken', action.payload.refreshToken);\n      }\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        loading: false,\n        error: null\n      };\n\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.REGISTER_FAILURE:\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: false,\n        error: action.payload\n      };\n\n    case AUTH_ACTIONS.LOGOUT:\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      return {\n        ...initialState,\n        loading: false\n      };\n\n    case AUTH_ACTIONS.LOAD_USER:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        loading: false,\n        initialized: true\n      };\n\n    case AUTH_ACTIONS.SET_INITIALIZED:\n      return {\n        ...state,\n        loading: false,\n        initialized: true\n      };\n\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n\n    default:\n      return state;\n  }\n};\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n\n    // Response interceptor for handling token expiration\n    const responseInterceptor = api.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const currentToken = localStorage.getItem('token');\n        if (error.response?.status === 401 && currentToken) {\n          // Token expired, try to refresh\n          try {\n            const refreshToken = localStorage.getItem('refreshToken');\n            if (refreshToken) {\n              const response = await api.post('/auth/refresh-token', {\n                refreshToken\n              });\n\n              const newToken = response.data.data.token;\n              localStorage.setItem('token', newToken);\n              api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n\n              // Retry original request\n              error.config.headers['Authorization'] = `Bearer ${newToken}`;\n              return api.request(error.config);\n            }\n          } catch (refreshError) {\n            // Refresh failed, logout user\n            dispatch({ type: AUTH_ACTIONS.LOGOUT });\n          }\n        }\n        return Promise.reject(error);\n      }\n    );\n\n    return () => {\n      api.interceptors.response.eject(responseInterceptor);\n    };\n  }, []);\n\n  // Load user on app start - ONLY RUN ONCE\n  useEffect(() => {\n    let isMounted = true;\n\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n\n      if (token && isMounted) {\n        try {\n          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n          const response = await api.get('/auth/profile');\n\n          if (isMounted) {\n            dispatch({\n              type: AUTH_ACTIONS.LOAD_USER,\n              payload: response.data.data.user\n            });\n          }\n        } catch (error) {\n          console.error('Failed to load user:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          delete api.defaults.headers.common['Authorization'];\n\n          if (isMounted) {\n            dispatch({ type: AUTH_ACTIONS.SET_INITIALIZED });\n          }\n        }\n      } else if (isMounted) {\n        dispatch({ type: AUTH_ACTIONS.SET_INITIALIZED });\n      }\n    };\n\n    initializeAuth();\n\n    return () => {\n      isMounted = false;\n    };\n  }, []); // EMPTY DEPENDENCY ARRAY - ONLY RUN ONCE ON MOUNT\n\n  // Login function\n  const login = async (email, password) => {\n    dispatch({ type: AUTH_ACTIONS.LOGIN_START });\n    try {\n      const response = await api.post('/auth/login', { email, password });\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: response.data.data\n      });\n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Register function\n  const register = async (userData) => {\n    dispatch({ type: AUTH_ACTIONS.REGISTER_START });\n    try {\n      const response = await api.post('/auth/register', userData);\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS,\n        payload: response.data.data\n      });\n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: errorMessage\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      dispatch({ type: AUTH_ACTIONS.LOGOUT });\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    clearError\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,WAAW,gBAAGf,aAAa,CAAC,CAAC;;AAEnC;AACA,MAAMgB,YAAY,GAAG;EACnBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,cAAc,EAAE,gBAAgB;EAChCC,gBAAgB,EAAE,kBAAkB;EACpCC,gBAAgB,EAAE,kBAAkB;EACpCC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,eAAe,EAAE;AACnB,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACpCC,eAAe,EAAE,CAAC,CAACF,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAChDE,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKvB,YAAY,CAACC,WAAW;IAC7B,KAAKD,YAAY,CAACK,cAAc;MAC9B,OAAO;QACL,GAAGgB,KAAK;QACRJ,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKlB,YAAY,CAACE,aAAa;IAC/B,KAAKF,YAAY,CAACM,gBAAgB;MAChCQ,YAAY,CAACU,OAAO,CAAC,OAAO,EAAEF,MAAM,CAACG,OAAO,CAACZ,KAAK,CAAC;MACnD,IAAIS,MAAM,CAACG,OAAO,CAACC,YAAY,EAAE;QAC/BZ,YAAY,CAACU,OAAO,CAAC,cAAc,EAAEF,MAAM,CAACG,OAAO,CAACC,YAAY,CAAC;MACnE;MACA,OAAO;QACL,GAAGL,KAAK;QACRT,IAAI,EAAEU,MAAM,CAACG,OAAO,CAACb,IAAI;QACzBC,KAAK,EAAES,MAAM,CAACG,OAAO,CAACZ,KAAK;QAC3BG,eAAe,EAAE,IAAI;QACrBC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKlB,YAAY,CAACG,aAAa;IAC/B,KAAKH,YAAY,CAACO,gBAAgB;MAChCO,YAAY,CAACa,UAAU,CAAC,OAAO,CAAC;MAChCb,YAAY,CAACa,UAAU,CAAC,cAAc,CAAC;MACvC,OAAO;QACL,GAAGN,KAAK;QACRT,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXG,eAAe,EAAE,KAAK;QACtBC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEI,MAAM,CAACG;MAChB,CAAC;IAEH,KAAKzB,YAAY,CAACI,MAAM;MACtBU,YAAY,CAACa,UAAU,CAAC,OAAO,CAAC;MAChCb,YAAY,CAACa,UAAU,CAAC,cAAc,CAAC;MACvC,OAAO;QACL,GAAGhB,YAAY;QACfM,OAAO,EAAE;MACX,CAAC;IAEH,KAAKjB,YAAY,CAACQ,SAAS;MACzB,OAAO;QACL,GAAGa,KAAK;QACRT,IAAI,EAAEU,MAAM,CAACG,OAAO;QACpBT,eAAe,EAAE,IAAI;QACrBC,OAAO,EAAE,KAAK;QACdE,WAAW,EAAE;MACf,CAAC;IAEH,KAAKnB,YAAY,CAACU,eAAe;MAC/B,OAAO;QACL,GAAGW,KAAK;QACRJ,OAAO,EAAE,KAAK;QACdE,WAAW,EAAE;MACf,CAAC;IAEH,KAAKnB,YAAY,CAACS,WAAW;MAC3B,OAAO;QACL,GAAGY,KAAK;QACRH,KAAK,EAAE;MACT,CAAC;IAEH;MACE,OAAOG,KAAK;EAChB;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACT,KAAK,EAAEU,QAAQ,CAAC,GAAG7C,UAAU,CAACkC,WAAW,EAAET,YAAY,CAAC;;EAE/D;EACAxB,SAAS,CAAC,MAAM;IACd,MAAM0B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTlB,GAAG,CAACqC,QAAQ,CAAClC,OAAO,CAACmC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUpB,KAAK,EAAE;IAClE;;IAEA;IACA,MAAMqB,mBAAmB,GAAGvC,GAAG,CAACwC,YAAY,CAACC,QAAQ,CAACC,GAAG,CACtDD,QAAQ,IAAKA,QAAQ,EACtB,MAAOlB,KAAK,IAAK;MAAA,IAAAoB,eAAA;MACf,MAAMC,YAAY,GAAGzB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAClD,IAAI,EAAAuB,eAAA,GAAApB,KAAK,CAACkB,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAID,YAAY,EAAE;QAClD;QACA,IAAI;UACF,MAAMb,YAAY,GAAGZ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;UACzD,IAAIW,YAAY,EAAE;YAChB,MAAMU,QAAQ,GAAG,MAAMzC,GAAG,CAAC8C,IAAI,CAAC,qBAAqB,EAAE;cACrDf;YACF,CAAC,CAAC;YAEF,MAAMgB,QAAQ,GAAGN,QAAQ,CAACO,IAAI,CAACA,IAAI,CAAC9B,KAAK;YACzCC,YAAY,CAACU,OAAO,CAAC,OAAO,EAAEkB,QAAQ,CAAC;YACvC/C,GAAG,CAACqC,QAAQ,CAAClC,OAAO,CAACmC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUS,QAAQ,EAAE;;YAEnE;YACAxB,KAAK,CAAC0B,MAAM,CAAC9C,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU4C,QAAQ,EAAE;YAC5D,OAAO/C,GAAG,CAACkD,OAAO,CAAC3B,KAAK,CAAC0B,MAAM,CAAC;UAClC;QACF,CAAC,CAAC,OAAOE,YAAY,EAAE;UACrB;UACAf,QAAQ,CAAC;YAAER,IAAI,EAAEvB,YAAY,CAACI;UAAO,CAAC,CAAC;QACzC;MACF;MACA,OAAO2C,OAAO,CAACC,MAAM,CAAC9B,KAAK,CAAC;IAC9B,CACF,CAAC;IAED,OAAO,MAAM;MACXvB,GAAG,CAACwC,YAAY,CAACC,QAAQ,CAACa,KAAK,CAACf,mBAAmB,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/C,SAAS,CAAC,MAAM;IACd,IAAI+D,SAAS,GAAG,IAAI;IAEpB,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAMtC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAIF,KAAK,IAAIqC,SAAS,EAAE;QACtB,IAAI;UACFvD,GAAG,CAACqC,QAAQ,CAAClC,OAAO,CAACmC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUpB,KAAK,EAAE;UAChE,MAAMuB,QAAQ,GAAG,MAAMzC,GAAG,CAACyD,GAAG,CAAC,eAAe,CAAC;UAE/C,IAAIF,SAAS,EAAE;YACbnB,QAAQ,CAAC;cACPR,IAAI,EAAEvB,YAAY,CAACQ,SAAS;cAC5BiB,OAAO,EAAEW,QAAQ,CAACO,IAAI,CAACA,IAAI,CAAC/B;YAC9B,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAOM,KAAK,EAAE;UACdmC,OAAO,CAACnC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5CJ,YAAY,CAACa,UAAU,CAAC,OAAO,CAAC;UAChCb,YAAY,CAACa,UAAU,CAAC,cAAc,CAAC;UACvC,OAAOhC,GAAG,CAACqC,QAAQ,CAAClC,OAAO,CAACmC,MAAM,CAAC,eAAe,CAAC;UAEnD,IAAIiB,SAAS,EAAE;YACbnB,QAAQ,CAAC;cAAER,IAAI,EAAEvB,YAAY,CAACU;YAAgB,CAAC,CAAC;UAClD;QACF;MACF,CAAC,MAAM,IAAIwC,SAAS,EAAE;QACpBnB,QAAQ,CAAC;UAAER,IAAI,EAAEvB,YAAY,CAACU;QAAgB,CAAC,CAAC;MAClD;IACF,CAAC;IAEDyC,cAAc,CAAC,CAAC;IAEhB,OAAO,MAAM;MACXD,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMI,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvCzB,QAAQ,CAAC;MAAER,IAAI,EAAEvB,YAAY,CAACC;IAAY,CAAC,CAAC;IAC5C,IAAI;MACF,MAAMmC,QAAQ,GAAG,MAAMzC,GAAG,CAAC8C,IAAI,CAAC,aAAa,EAAE;QAAEc,KAAK;QAAEC;MAAS,CAAC,CAAC;MACnEzB,QAAQ,CAAC;QACPR,IAAI,EAAEvB,YAAY,CAACE,aAAa;QAChCuB,OAAO,EAAEW,QAAQ,CAACO,IAAI,CAACA;MACzB,CAAC,CAAC;MACF,OAAO;QAAEc,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOvC,KAAK,EAAE;MAAA,IAAAwC,gBAAA,EAAAC,qBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,gBAAA,GAAAxC,KAAK,CAACkB,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAAI,cAAc;MACpE9B,QAAQ,CAAC;QACPR,IAAI,EAAEvB,YAAY,CAACG,aAAa;QAChCsB,OAAO,EAAEmC;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEvC,KAAK,EAAE0C;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAME,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnChC,QAAQ,CAAC;MAAER,IAAI,EAAEvB,YAAY,CAACK;IAAe,CAAC,CAAC;IAC/C,IAAI;MACF,MAAM+B,QAAQ,GAAG,MAAMzC,GAAG,CAAC8C,IAAI,CAAC,gBAAgB,EAAEsB,QAAQ,CAAC;MAC3DhC,QAAQ,CAAC;QACPR,IAAI,EAAEvB,YAAY,CAACM,gBAAgB;QACnCmB,OAAO,EAAEW,QAAQ,CAACO,IAAI,CAACA;MACzB,CAAC,CAAC;MACF,OAAO;QAAEc,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOvC,KAAK,EAAE;MAAA,IAAA8C,gBAAA,EAAAC,qBAAA;MACd,MAAML,YAAY,GAAG,EAAAI,gBAAA,GAAA9C,KAAK,CAACkB,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,qBAAqB;MAC3E9B,QAAQ,CAAC;QACPR,IAAI,EAAEvB,YAAY,CAACO,gBAAgB;QACnCkB,OAAO,EAAEmC;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEvC,KAAK,EAAE0C;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMM,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMvE,GAAG,CAAC8C,IAAI,CAAC,cAAc,CAAC;IAChC,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRa,QAAQ,CAAC;QAAER,IAAI,EAAEvB,YAAY,CAACI;MAAO,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAM+D,UAAU,GAAGA,CAAA,KAAM;IACvBpC,QAAQ,CAAC;MAAER,IAAI,EAAEvB,YAAY,CAACS;IAAY,CAAC,CAAC;EAC9C,CAAC;EAED,MAAM2D,KAAK,GAAG;IACZ,GAAG/C,KAAK;IACRiC,KAAK;IACLQ,QAAQ;IACRI,MAAM;IACNC;EACF,CAAC;EAED,oBACE7E,OAAA,CAACS,WAAW,CAACsE,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvC,QAAA,EAChCA;EAAQ;IAAAyC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAA3C,EAAA,CA7JaF,YAAY;AAAA8C,EAAA,GAAZ9C,YAAY;AA8JzB,OAAO,MAAM+C,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG5F,UAAU,CAACc,WAAW,CAAC;EACvC,IAAI,CAAC8E,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAe5E,WAAW;AAAC,IAAA2E,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}