const { pool } = require('../config/database');
const bcrypt = require('bcryptjs');

class User {
  constructor(userData) {
    this.id = userData.id;
    this.username = userData.username;
    this.email = userData.email;
    this.password_hash = userData.password_hash;
    this.first_name = userData.first_name;
    this.last_name = userData.last_name;
    this.is_active = userData.is_active;
    this.created_at = userData.created_at;
    this.updated_at = userData.updated_at;
    this.last_login = userData.last_login;
  }

  // Create a new user
  static async create(userData) {
    try {
      const { username, email, password, first_name, last_name } = userData;
      
      // Hash password
      const saltRounds = 12;
      const password_hash = await bcrypt.hash(password, saltRounds);

      const query = `
        INSERT INTO users (username, email, password_hash, first_name, last_name)
        VALUES (?, ?, ?, ?, ?)
      `;

      const [result] = await pool.execute(query, [
        username,
        email,
        password_hash,
        first_name || null,
        last_name || null
      ]);

      return await User.findById(result.insertId);
    } catch (error) {
      throw error;
    }
  }

  // Find user by ID
  static async findById(id) {
    try {
      const query = 'SELECT * FROM users WHERE id = ? AND is_active = true';
      const [rows] = await pool.execute(query, [id]);
      
      if (rows.length === 0) {
        return null;
      }

      return new User(rows[0]);
    } catch (error) {
      throw error;
    }
  }

  // Find user by email
  static async findByEmail(email) {
    try {
      const query = 'SELECT * FROM users WHERE email = ? AND is_active = true';
      const [rows] = await pool.execute(query, [email]);
      
      if (rows.length === 0) {
        return null;
      }

      return new User(rows[0]);
    } catch (error) {
      throw error;
    }
  }

  // Find user by username
  static async findByUsername(username) {
    try {
      const query = 'SELECT * FROM users WHERE username = ? AND is_active = true';
      const [rows] = await pool.execute(query, [username]);
      
      if (rows.length === 0) {
        return null;
      }

      return new User(rows[0]);
    } catch (error) {
      throw error;
    }
  }

  // Verify password
  async verifyPassword(password) {
    try {
      return await bcrypt.compare(password, this.password_hash);
    } catch (error) {
      throw error;
    }
  }

  // Update last login
  async updateLastLogin() {
    try {
      const query = 'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?';
      await pool.execute(query, [this.id]);
      this.last_login = new Date();
    } catch (error) {
      throw error;
    }
  }

  // Get user data without sensitive information
  toJSON() {
    return {
      id: this.id,
      username: this.username,
      email: this.email,
      first_name: this.first_name,
      last_name: this.last_name,
      created_at: this.created_at,
      last_login: this.last_login
    };
  }

  // Get all users (admin function)
  static async findAll(limit = 50, offset = 0) {
    try {
      const query = `
        SELECT id, username, email, first_name, last_name, created_at, last_login, is_active
        FROM users 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
      `;
      const [rows] = await pool.execute(query, [limit, offset]);
      
      return rows.map(row => new User(row));
    } catch (error) {
      throw error;
    }
  }

  // Check if email exists
  static async emailExists(email) {
    try {
      const query = 'SELECT COUNT(*) as count FROM users WHERE email = ?';
      const [rows] = await pool.execute(query, [email]);
      return rows[0].count > 0;
    } catch (error) {
      throw error;
    }
  }

  // Check if username exists
  static async usernameExists(username) {
    try {
      const query = 'SELECT COUNT(*) as count FROM users WHERE username = ?';
      const [rows] = await pool.execute(query, [username]);
      return rows[0].count > 0;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = User;
