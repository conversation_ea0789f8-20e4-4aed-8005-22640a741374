{"ast": null, "code": "var _jsxFileName = \"D:\\\\mysql-project\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect, useRef } from 'react';\nimport axios from 'axios';\n\n// API base URL\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Auth context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Auth actions\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  REGISTER_START: 'REGISTER_START',\n  REGISTER_SUCCESS: 'REGISTER_SUCCESS',\n  REGISTER_FAILURE: 'REGISTER_FAILURE',\n  LOAD_USER: 'LOAD_USER',\n  CLEAR_ERROR: 'CLEAR_ERROR'\n};\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isAuthenticated: false,\n  loading: true,\n  error: null\n};\n\n// Auth reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.REGISTER_START:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n    case AUTH_ACTIONS.REGISTER_SUCCESS:\n      localStorage.setItem('token', action.payload.token);\n      if (action.payload.refreshToken) {\n        localStorage.setItem('refreshToken', action.payload.refreshToken);\n      }\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        loading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.REGISTER_FAILURE:\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      return {\n        ...initialState,\n        loading: false\n      };\n    case AUTH_ACTIONS.LOAD_USER:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        loading: false\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n\n    // Response interceptor for handling token expiration\n    const responseInterceptor = api.interceptors.response.use(response => response, async error => {\n      var _error$response;\n      const currentToken = localStorage.getItem('token');\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && currentToken) {\n        // Token expired, try to refresh\n        try {\n          const refreshToken = localStorage.getItem('refreshToken');\n          if (refreshToken) {\n            const response = await api.post('/auth/refresh-token', {\n              refreshToken\n            });\n            const newToken = response.data.data.token;\n            localStorage.setItem('token', newToken);\n            api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n\n            // Retry original request\n            error.config.headers['Authorization'] = `Bearer ${newToken}`;\n            return api.request(error.config);\n          }\n        } catch (refreshError) {\n          // Refresh failed, logout user\n          dispatch({\n            type: AUTH_ACTIONS.LOGOUT\n          });\n        }\n      }\n      return Promise.reject(error);\n    });\n    return () => {\n      api.interceptors.response.eject(responseInterceptor);\n    };\n  }, []);\n\n  // Load user on app start\n  useEffect(() => {\n    const loadUser = async () => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n          const response = await api.get('/auth/profile');\n          dispatch({\n            type: AUTH_ACTIONS.LOAD_USER,\n            payload: response.data.data.user\n          });\n        } catch (error) {\n          console.error('Failed to load user:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          dispatch({\n            type: AUTH_ACTIONS.LOGOUT\n          });\n        }\n      } else {\n        dispatch({\n          type: AUTH_ACTIONS.LOGOUT\n        });\n      }\n    };\n    loadUser();\n  }, []); // Empty dependency array - only run once on mount\n\n  // Login function\n  const login = async (email, password) => {\n    dispatch({\n      type: AUTH_ACTIONS.LOGIN_START\n    });\n    try {\n      const response = await api.post('/auth/login', {\n        email,\n        password\n      });\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: response.data.data\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Register function\n  const register = async userData => {\n    dispatch({\n      type: AUTH_ACTIONS.REGISTER_START\n    });\n    try {\n      const response = await api.post('/auth/register', userData);\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS,\n        payload: response.data.data\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      dispatch({\n        type: AUTH_ACTIONS.LOGOUT\n      });\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 244,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"GUSXxL/WUElrtHc/X73NyHNRMdw=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "useRef", "axios", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "AuthContext", "AUTH_ACTIONS", "LOGIN_START", "LOGIN_SUCCESS", "LOGIN_FAILURE", "LOGOUT", "REGISTER_START", "REGISTER_SUCCESS", "REGISTER_FAILURE", "LOAD_USER", "CLEAR_ERROR", "initialState", "user", "token", "localStorage", "getItem", "isAuthenticated", "loading", "error", "authReducer", "state", "action", "type", "setItem", "payload", "refreshToken", "removeItem", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "defaults", "common", "responseInterceptor", "interceptors", "response", "use", "_error$response", "currentToken", "status", "post", "newToken", "data", "config", "request", "refreshError", "Promise", "reject", "eject", "loadUser", "get", "console", "login", "email", "password", "success", "_error$response2", "_error$response2$data", "errorMessage", "message", "register", "userData", "_error$response3", "_error$response3$data", "logout", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/mysql-project/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect, useRef } from 'react';\nimport axios from 'axios';\n\n// API base URL\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Auth context\nconst AuthContext = createContext();\n\n// Auth actions\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  REGISTER_START: 'REGISTER_START',\n  REGISTER_SUCCESS: 'REGISTER_SUCCESS',\n  REGISTER_FAILURE: 'REGISTER_FAILURE',\n  LOAD_USER: 'LOAD_USER',\n  CLEAR_ERROR: 'CLEAR_ERROR'\n};\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isAuthenticated: false,\n  loading: true,\n  error: null\n};\n\n// Auth reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n    case AUTH_ACTIONS.REGISTER_START:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n    case AUTH_ACTIONS.REGISTER_SUCCESS:\n      localStorage.setItem('token', action.payload.token);\n      if (action.payload.refreshToken) {\n        localStorage.setItem('refreshToken', action.payload.refreshToken);\n      }\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        loading: false,\n        error: null\n      };\n\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n    case AUTH_ACTIONS.REGISTER_FAILURE:\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        loading: false,\n        error: action.payload\n      };\n\n    case AUTH_ACTIONS.LOGOUT:\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      return {\n        ...initialState,\n        loading: false\n      };\n\n    case AUTH_ACTIONS.LOAD_USER:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        loading: false\n      };\n\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n\n    default:\n      return state;\n  }\n};\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n\n    // Response interceptor for handling token expiration\n    const responseInterceptor = api.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const currentToken = localStorage.getItem('token');\n        if (error.response?.status === 401 && currentToken) {\n          // Token expired, try to refresh\n          try {\n            const refreshToken = localStorage.getItem('refreshToken');\n            if (refreshToken) {\n              const response = await api.post('/auth/refresh-token', {\n                refreshToken\n              });\n\n              const newToken = response.data.data.token;\n              localStorage.setItem('token', newToken);\n              api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n\n              // Retry original request\n              error.config.headers['Authorization'] = `Bearer ${newToken}`;\n              return api.request(error.config);\n            }\n          } catch (refreshError) {\n            // Refresh failed, logout user\n            dispatch({ type: AUTH_ACTIONS.LOGOUT });\n          }\n        }\n        return Promise.reject(error);\n      }\n    );\n\n    return () => {\n      api.interceptors.response.eject(responseInterceptor);\n    };\n  }, []);\n\n  // Load user on app start\n  useEffect(() => {\n    const loadUser = async () => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n          const response = await api.get('/auth/profile');\n          dispatch({\n            type: AUTH_ACTIONS.LOAD_USER,\n            payload: response.data.data.user\n          });\n        } catch (error) {\n          console.error('Failed to load user:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          dispatch({ type: AUTH_ACTIONS.LOGOUT });\n        }\n      } else {\n        dispatch({ type: AUTH_ACTIONS.LOGOUT });\n      }\n    };\n\n    loadUser();\n  }, []); // Empty dependency array - only run once on mount\n\n  // Login function\n  const login = async (email, password) => {\n    dispatch({ type: AUTH_ACTIONS.LOGIN_START });\n    try {\n      const response = await api.post('/auth/login', { email, password });\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: response.data.data\n      });\n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: errorMessage\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Register function\n  const register = async (userData) => {\n    dispatch({ type: AUTH_ACTIONS.REGISTER_START });\n    try {\n      const response = await api.post('/auth/register', userData);\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_SUCCESS,\n        payload: response.data.data\n      });\n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.REGISTER_FAILURE,\n        payload: errorMessage\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      dispatch({ type: AUTH_ACTIONS.LOGOUT });\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    clearError\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACvF,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,WAAW,gBAAGhB,aAAa,CAAC,CAAC;;AAEnC;AACA,MAAMiB,YAAY,GAAG;EACnBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,cAAc,EAAE,gBAAgB;EAChCC,gBAAgB,EAAE,kBAAkB;EACpCC,gBAAgB,EAAE,kBAAkB;EACpCC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACpCC,eAAe,EAAE,KAAK;EACtBC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKrB,YAAY,CAACC,WAAW;IAC7B,KAAKD,YAAY,CAACK,cAAc;MAC9B,OAAO;QACL,GAAGc,KAAK;QACRH,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKjB,YAAY,CAACE,aAAa;IAC/B,KAAKF,YAAY,CAACM,gBAAgB;MAChCO,YAAY,CAACS,OAAO,CAAC,OAAO,EAAEF,MAAM,CAACG,OAAO,CAACX,KAAK,CAAC;MACnD,IAAIQ,MAAM,CAACG,OAAO,CAACC,YAAY,EAAE;QAC/BX,YAAY,CAACS,OAAO,CAAC,cAAc,EAAEF,MAAM,CAACG,OAAO,CAACC,YAAY,CAAC;MACnE;MACA,OAAO;QACL,GAAGL,KAAK;QACRR,IAAI,EAAES,MAAM,CAACG,OAAO,CAACZ,IAAI;QACzBC,KAAK,EAAEQ,MAAM,CAACG,OAAO,CAACX,KAAK;QAC3BG,eAAe,EAAE,IAAI;QACrBC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKjB,YAAY,CAACG,aAAa;IAC/B,KAAKH,YAAY,CAACO,gBAAgB;MAChCM,YAAY,CAACY,UAAU,CAAC,OAAO,CAAC;MAChCZ,YAAY,CAACY,UAAU,CAAC,cAAc,CAAC;MACvC,OAAO;QACL,GAAGN,KAAK;QACRR,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXG,eAAe,EAAE,KAAK;QACtBC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEG,MAAM,CAACG;MAChB,CAAC;IAEH,KAAKvB,YAAY,CAACI,MAAM;MACtBS,YAAY,CAACY,UAAU,CAAC,OAAO,CAAC;MAChCZ,YAAY,CAACY,UAAU,CAAC,cAAc,CAAC;MACvC,OAAO;QACL,GAAGf,YAAY;QACfM,OAAO,EAAE;MACX,CAAC;IAEH,KAAKhB,YAAY,CAACQ,SAAS;MACzB,OAAO;QACL,GAAGW,KAAK;QACRR,IAAI,EAAES,MAAM,CAACG,OAAO;QACpBR,eAAe,EAAE,IAAI;QACrBC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKhB,YAAY,CAACS,WAAW;MAC3B,OAAO;QACL,GAAGU,KAAK;QACRF,KAAK,EAAE;MACT,CAAC;IAEH;MACE,OAAOE,KAAK;EAChB;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACT,KAAK,EAAEU,QAAQ,CAAC,GAAG5C,UAAU,CAACiC,WAAW,EAAER,YAAY,CAAC;;EAE/D;EACAxB,SAAS,CAAC,MAAM;IACd,MAAM0B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTjB,GAAG,CAACmC,QAAQ,CAAChC,OAAO,CAACiC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUnB,KAAK,EAAE;IAClE;;IAEA;IACA,MAAMoB,mBAAmB,GAAGrC,GAAG,CAACsC,YAAY,CAACC,QAAQ,CAACC,GAAG,CACtDD,QAAQ,IAAKA,QAAQ,EACtB,MAAOjB,KAAK,IAAK;MAAA,IAAAmB,eAAA;MACf,MAAMC,YAAY,GAAGxB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAClD,IAAI,EAAAsB,eAAA,GAAAnB,KAAK,CAACiB,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAID,YAAY,EAAE;QAClD;QACA,IAAI;UACF,MAAMb,YAAY,GAAGX,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;UACzD,IAAIU,YAAY,EAAE;YAChB,MAAMU,QAAQ,GAAG,MAAMvC,GAAG,CAAC4C,IAAI,CAAC,qBAAqB,EAAE;cACrDf;YACF,CAAC,CAAC;YAEF,MAAMgB,QAAQ,GAAGN,QAAQ,CAACO,IAAI,CAACA,IAAI,CAAC7B,KAAK;YACzCC,YAAY,CAACS,OAAO,CAAC,OAAO,EAAEkB,QAAQ,CAAC;YACvC7C,GAAG,CAACmC,QAAQ,CAAChC,OAAO,CAACiC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUS,QAAQ,EAAE;;YAEnE;YACAvB,KAAK,CAACyB,MAAM,CAAC5C,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU0C,QAAQ,EAAE;YAC5D,OAAO7C,GAAG,CAACgD,OAAO,CAAC1B,KAAK,CAACyB,MAAM,CAAC;UAClC;QACF,CAAC,CAAC,OAAOE,YAAY,EAAE;UACrB;UACAf,QAAQ,CAAC;YAAER,IAAI,EAAErB,YAAY,CAACI;UAAO,CAAC,CAAC;QACzC;MACF;MACA,OAAOyC,OAAO,CAACC,MAAM,CAAC7B,KAAK,CAAC;IAC9B,CACF,CAAC;IAED,OAAO,MAAM;MACXtB,GAAG,CAACsC,YAAY,CAACC,QAAQ,CAACa,KAAK,CAACf,mBAAmB,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9C,SAAS,CAAC,MAAM;IACd,MAAM8D,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,MAAMpC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACT,IAAI;UACFjB,GAAG,CAACmC,QAAQ,CAAChC,OAAO,CAACiC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUnB,KAAK,EAAE;UAChE,MAAMsB,QAAQ,GAAG,MAAMvC,GAAG,CAACsD,GAAG,CAAC,eAAe,CAAC;UAC/CpB,QAAQ,CAAC;YACPR,IAAI,EAAErB,YAAY,CAACQ,SAAS;YAC5Be,OAAO,EAAEW,QAAQ,CAACO,IAAI,CAACA,IAAI,CAAC9B;UAC9B,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOM,KAAK,EAAE;UACdiC,OAAO,CAACjC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5CJ,YAAY,CAACY,UAAU,CAAC,OAAO,CAAC;UAChCZ,YAAY,CAACY,UAAU,CAAC,cAAc,CAAC;UACvCI,QAAQ,CAAC;YAAER,IAAI,EAAErB,YAAY,CAACI;UAAO,CAAC,CAAC;QACzC;MACF,CAAC,MAAM;QACLyB,QAAQ,CAAC;UAAER,IAAI,EAAErB,YAAY,CAACI;QAAO,CAAC,CAAC;MACzC;IACF,CAAC;IAED4C,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMG,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvCxB,QAAQ,CAAC;MAAER,IAAI,EAAErB,YAAY,CAACC;IAAY,CAAC,CAAC;IAC5C,IAAI;MACF,MAAMiC,QAAQ,GAAG,MAAMvC,GAAG,CAAC4C,IAAI,CAAC,aAAa,EAAE;QAAEa,KAAK;QAAEC;MAAS,CAAC,CAAC;MACnExB,QAAQ,CAAC;QACPR,IAAI,EAAErB,YAAY,CAACE,aAAa;QAChCqB,OAAO,EAAEW,QAAQ,CAACO,IAAI,CAACA;MACzB,CAAC,CAAC;MACF,OAAO;QAAEa,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOrC,KAAK,EAAE;MAAA,IAAAsC,gBAAA,EAAAC,qBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,gBAAA,GAAAtC,KAAK,CAACiB,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,IAAI,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAAI,cAAc;MACpE7B,QAAQ,CAAC;QACPR,IAAI,EAAErB,YAAY,CAACG,aAAa;QAChCoB,OAAO,EAAEkC;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAErC,KAAK,EAAEwC;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAME,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC/B,QAAQ,CAAC;MAAER,IAAI,EAAErB,YAAY,CAACK;IAAe,CAAC,CAAC;IAC/C,IAAI;MACF,MAAM6B,QAAQ,GAAG,MAAMvC,GAAG,CAAC4C,IAAI,CAAC,gBAAgB,EAAEqB,QAAQ,CAAC;MAC3D/B,QAAQ,CAAC;QACPR,IAAI,EAAErB,YAAY,CAACM,gBAAgB;QACnCiB,OAAO,EAAEW,QAAQ,CAACO,IAAI,CAACA;MACzB,CAAC,CAAC;MACF,OAAO;QAAEa,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOrC,KAAK,EAAE;MAAA,IAAA4C,gBAAA,EAAAC,qBAAA;MACd,MAAML,YAAY,GAAG,EAAAI,gBAAA,GAAA5C,KAAK,CAACiB,QAAQ,cAAA2B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,qBAAqB;MAC3E7B,QAAQ,CAAC;QACPR,IAAI,EAAErB,YAAY,CAACO,gBAAgB;QACnCgB,OAAO,EAAEkC;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAErC,KAAK,EAAEwC;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMM,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMpE,GAAG,CAAC4C,IAAI,CAAC,cAAc,CAAC;IAChC,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdiC,OAAO,CAACjC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRY,QAAQ,CAAC;QAAER,IAAI,EAAErB,YAAY,CAACI;MAAO,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAM4D,UAAU,GAAGA,CAAA,KAAM;IACvBnC,QAAQ,CAAC;MAAER,IAAI,EAAErB,YAAY,CAACS;IAAY,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMwD,KAAK,GAAG;IACZ,GAAG9C,KAAK;IACRgC,KAAK;IACLQ,QAAQ;IACRI,MAAM;IACNC;EACF,CAAC;EAED,oBACE1E,OAAA,CAACS,WAAW,CAACmE,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAtC,QAAA,EAChCA;EAAQ;IAAAwC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAA1C,EAAA,CA/IaF,YAAY;AAAA6C,EAAA,GAAZ7C,YAAY;AAgJzB,OAAO,MAAM8C,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG1F,UAAU,CAACe,WAAW,CAAC;EACvC,IAAI,CAAC2E,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAezE,WAAW;AAAC,IAAAwE,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}