{"ast": null, "code": "/** @license React vundefined\n * react-refresh-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    // ATTENTION\n    // When adding new symbols to this file,\n    // Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n    // The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n    // nor polyfill, then a plain number is used for performance.\n    var REACT_ELEMENT_TYPE = 0xeac7;\n    var REACT_PORTAL_TYPE = 0xeaca;\n    var REACT_FRAGMENT_TYPE = 0xeacb;\n    var REACT_STRICT_MODE_TYPE = 0xeacc;\n    var REACT_PROFILER_TYPE = 0xead2;\n    var REACT_PROVIDER_TYPE = 0xeacd;\n    var REACT_CONTEXT_TYPE = 0xeace;\n    var REACT_FORWARD_REF_TYPE = 0xead0;\n    var REACT_SUSPENSE_TYPE = 0xead1;\n    var REACT_SUSPENSE_LIST_TYPE = 0xead8;\n    var REACT_MEMO_TYPE = 0xead3;\n    var REACT_LAZY_TYPE = 0xead4;\n    var REACT_SCOPE_TYPE = 0xead7;\n    var REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\n    var REACT_OFFSCREEN_TYPE = 0xeae2;\n    var REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n    var REACT_CACHE_TYPE = 0xeae4;\n    if (typeof Symbol === 'function' && Symbol.for) {\n      var symbolFor = Symbol.for;\n      REACT_ELEMENT_TYPE = symbolFor('react.element');\n      REACT_PORTAL_TYPE = symbolFor('react.portal');\n      REACT_FRAGMENT_TYPE = symbolFor('react.fragment');\n      REACT_STRICT_MODE_TYPE = symbolFor('react.strict_mode');\n      REACT_PROFILER_TYPE = symbolFor('react.profiler');\n      REACT_PROVIDER_TYPE = symbolFor('react.provider');\n      REACT_CONTEXT_TYPE = symbolFor('react.context');\n      REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n      REACT_SUSPENSE_TYPE = symbolFor('react.suspense');\n      REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n      REACT_MEMO_TYPE = symbolFor('react.memo');\n      REACT_LAZY_TYPE = symbolFor('react.lazy');\n      REACT_SCOPE_TYPE = symbolFor('react.scope');\n      REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n      REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n      REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n      REACT_CACHE_TYPE = symbolFor('react.cache');\n    }\n    var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map; // We never remove these associations.\n    // It's OK to reference families, but use WeakMap/Set for types.\n\n    var allFamiliesByID = new Map();\n    var allFamiliesByType = new PossiblyWeakMap();\n    var allSignaturesByType = new PossiblyWeakMap(); // This WeakMap is read by React, so we only put families\n    // that have actually been edited here. This keeps checks fast.\n    // $FlowIssue\n\n    var updatedFamiliesByType = new PossiblyWeakMap(); // This is cleared on every performReactRefresh() call.\n    // It is an array of [Family, NextType] tuples.\n\n    var pendingUpdates = []; // This is injected by the renderer via DevTools global hook.\n\n    var helpersByRendererID = new Map();\n    var helpersByRoot = new Map(); // We keep track of mounted roots so we can schedule updates.\n\n    var mountedRoots = new Set(); // If a root captures an error, we remember it so we can retry on edit.\n\n    var failedRoots = new Set(); // In environments that support WeakMap, we also remember the last element for every root.\n    // It needs to be weak because we do this even for roots that failed to mount.\n    // If there is no WeakMap, we won't attempt to do retrying.\n    // $FlowIssue\n\n    var rootElements =\n    // $FlowIssue\n    typeof WeakMap === 'function' ? new WeakMap() : null;\n    var isPerformingRefresh = false;\n    function computeFullKey(signature) {\n      if (signature.fullKey !== null) {\n        return signature.fullKey;\n      }\n      var fullKey = signature.ownKey;\n      var hooks;\n      try {\n        hooks = signature.getCustomHooks();\n      } catch (err) {\n        // This can happen in an edge case, e.g. if expression like Foo.useSomething\n        // depends on Foo which is lazily initialized during rendering.\n        // In that case just assume we'll have to remount.\n        signature.forceReset = true;\n        signature.fullKey = fullKey;\n        return fullKey;\n      }\n      for (var i = 0; i < hooks.length; i++) {\n        var hook = hooks[i];\n        if (typeof hook !== 'function') {\n          // Something's wrong. Assume we need to remount.\n          signature.forceReset = true;\n          signature.fullKey = fullKey;\n          return fullKey;\n        }\n        var nestedHookSignature = allSignaturesByType.get(hook);\n        if (nestedHookSignature === undefined) {\n          // No signature means Hook wasn't in the source code, e.g. in a library.\n          // We'll skip it because we can assume it won't change during this session.\n          continue;\n        }\n        var nestedHookKey = computeFullKey(nestedHookSignature);\n        if (nestedHookSignature.forceReset) {\n          signature.forceReset = true;\n        }\n        fullKey += '\\n---\\n' + nestedHookKey;\n      }\n      signature.fullKey = fullKey;\n      return fullKey;\n    }\n    function haveEqualSignatures(prevType, nextType) {\n      var prevSignature = allSignaturesByType.get(prevType);\n      var nextSignature = allSignaturesByType.get(nextType);\n      if (prevSignature === undefined && nextSignature === undefined) {\n        return true;\n      }\n      if (prevSignature === undefined || nextSignature === undefined) {\n        return false;\n      }\n      if (computeFullKey(prevSignature) !== computeFullKey(nextSignature)) {\n        return false;\n      }\n      if (nextSignature.forceReset) {\n        return false;\n      }\n      return true;\n    }\n    function isReactClass(type) {\n      return type.prototype && type.prototype.isReactComponent;\n    }\n    function canPreserveStateBetween(prevType, nextType) {\n      if (isReactClass(prevType) || isReactClass(nextType)) {\n        return false;\n      }\n      if (haveEqualSignatures(prevType, nextType)) {\n        return true;\n      }\n      return false;\n    }\n    function resolveFamily(type) {\n      // Only check updated types to keep lookups fast.\n      return updatedFamiliesByType.get(type);\n    } // If we didn't care about IE11, we could use new Map/Set(iterable).\n\n    function cloneMap(map) {\n      var clone = new Map();\n      map.forEach(function (value, key) {\n        clone.set(key, value);\n      });\n      return clone;\n    }\n    function cloneSet(set) {\n      var clone = new Set();\n      set.forEach(function (value) {\n        clone.add(value);\n      });\n      return clone;\n    } // This is a safety mechanism to protect against rogue getters and Proxies.\n\n    function getProperty(object, property) {\n      try {\n        return object[property];\n      } catch (err) {\n        // Intentionally ignore.\n        return undefined;\n      }\n    }\n    function performReactRefresh() {\n      if (pendingUpdates.length === 0) {\n        return null;\n      }\n      if (isPerformingRefresh) {\n        return null;\n      }\n      isPerformingRefresh = true;\n      try {\n        var staleFamilies = new Set();\n        var updatedFamilies = new Set();\n        var updates = pendingUpdates;\n        pendingUpdates = [];\n        updates.forEach(function (_ref) {\n          var family = _ref[0],\n            nextType = _ref[1];\n          // Now that we got a real edit, we can create associations\n          // that will be read by the React reconciler.\n          var prevType = family.current;\n          updatedFamiliesByType.set(prevType, family);\n          updatedFamiliesByType.set(nextType, family);\n          family.current = nextType; // Determine whether this should be a re-render or a re-mount.\n\n          if (canPreserveStateBetween(prevType, nextType)) {\n            updatedFamilies.add(family);\n          } else {\n            staleFamilies.add(family);\n          }\n        }); // TODO: rename these fields to something more meaningful.\n\n        var update = {\n          updatedFamilies: updatedFamilies,\n          // Families that will re-render preserving state\n          staleFamilies: staleFamilies // Families that will be remounted\n        };\n        helpersByRendererID.forEach(function (helpers) {\n          // Even if there are no roots, set the handler on first update.\n          // This ensures that if *new* roots are mounted, they'll use the resolve handler.\n          helpers.setRefreshHandler(resolveFamily);\n        });\n        var didError = false;\n        var firstError = null; // We snapshot maps and sets that are mutated during commits.\n        // If we don't do this, there is a risk they will be mutated while\n        // we iterate over them. For example, trying to recover a failed root\n        // may cause another root to be added to the failed list -- an infinite loop.\n\n        var failedRootsSnapshot = cloneSet(failedRoots);\n        var mountedRootsSnapshot = cloneSet(mountedRoots);\n        var helpersByRootSnapshot = cloneMap(helpersByRoot);\n        failedRootsSnapshot.forEach(function (root) {\n          var helpers = helpersByRootSnapshot.get(root);\n          if (helpers === undefined) {\n            throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n          }\n          if (!failedRoots.has(root)) {// No longer failed.\n          }\n          if (rootElements === null) {\n            return;\n          }\n          if (!rootElements.has(root)) {\n            return;\n          }\n          var element = rootElements.get(root);\n          try {\n            helpers.scheduleRoot(root, element);\n          } catch (err) {\n            if (!didError) {\n              didError = true;\n              firstError = err;\n            } // Keep trying other roots.\n          }\n        });\n        mountedRootsSnapshot.forEach(function (root) {\n          var helpers = helpersByRootSnapshot.get(root);\n          if (helpers === undefined) {\n            throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n          }\n          if (!mountedRoots.has(root)) {// No longer mounted.\n          }\n          try {\n            helpers.scheduleRefresh(root, update);\n          } catch (err) {\n            if (!didError) {\n              didError = true;\n              firstError = err;\n            } // Keep trying other roots.\n          }\n        });\n        if (didError) {\n          throw firstError;\n        }\n        return update;\n      } finally {\n        isPerformingRefresh = false;\n      }\n    }\n    function register(type, id) {\n      {\n        if (type === null) {\n          return;\n        }\n        if (typeof type !== 'function' && typeof type !== 'object') {\n          return;\n        } // This can happen in an edge case, e.g. if we register\n        // return value of a HOC but it returns a cached component.\n        // Ignore anything but the first registration for each type.\n\n        if (allFamiliesByType.has(type)) {\n          return;\n        } // Create family or remember to update it.\n        // None of this bookkeeping affects reconciliation\n        // until the first performReactRefresh() call above.\n\n        var family = allFamiliesByID.get(id);\n        if (family === undefined) {\n          family = {\n            current: type\n          };\n          allFamiliesByID.set(id, family);\n        } else {\n          pendingUpdates.push([family, type]);\n        }\n        allFamiliesByType.set(type, family); // Visit inner types because we might not have registered them.\n\n        if (typeof type === 'object' && type !== null) {\n          switch (getProperty(type, '$$typeof')) {\n            case REACT_FORWARD_REF_TYPE:\n              register(type.render, id + '$render');\n              break;\n            case REACT_MEMO_TYPE:\n              register(type.type, id + '$type');\n              break;\n          }\n        }\n      }\n    }\n    function setSignature(type, key) {\n      var forceReset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var getCustomHooks = arguments.length > 3 ? arguments[3] : undefined;\n      {\n        if (!allSignaturesByType.has(type)) {\n          allSignaturesByType.set(type, {\n            forceReset: forceReset,\n            ownKey: key,\n            fullKey: null,\n            getCustomHooks: getCustomHooks || function () {\n              return [];\n            }\n          });\n        } // Visit inner types because we might not have signed them.\n\n        if (typeof type === 'object' && type !== null) {\n          switch (getProperty(type, '$$typeof')) {\n            case REACT_FORWARD_REF_TYPE:\n              setSignature(type.render, key, forceReset, getCustomHooks);\n              break;\n            case REACT_MEMO_TYPE:\n              setSignature(type.type, key, forceReset, getCustomHooks);\n              break;\n          }\n        }\n      }\n    } // This is lazily called during first render for a type.\n    // It captures Hook list at that time so inline requires don't break comparisons.\n\n    function collectCustomHooksForSignature(type) {\n      {\n        var signature = allSignaturesByType.get(type);\n        if (signature !== undefined) {\n          computeFullKey(signature);\n        }\n      }\n    }\n    function getFamilyByID(id) {\n      {\n        return allFamiliesByID.get(id);\n      }\n    }\n    function getFamilyByType(type) {\n      {\n        return allFamiliesByType.get(type);\n      }\n    }\n    function findAffectedHostInstances(families) {\n      {\n        var affectedInstances = new Set();\n        mountedRoots.forEach(function (root) {\n          var helpers = helpersByRoot.get(root);\n          if (helpers === undefined) {\n            throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n          }\n          var instancesForRoot = helpers.findHostInstancesForRefresh(root, families);\n          instancesForRoot.forEach(function (inst) {\n            affectedInstances.add(inst);\n          });\n        });\n        return affectedInstances;\n      }\n    }\n    function injectIntoGlobalHook(globalObject) {\n      {\n        // For React Native, the global hook will be set up by require('react-devtools-core').\n        // That code will run before us. So we need to monkeypatch functions on existing hook.\n        // For React Web, the global hook will be set up by the extension.\n        // This will also run before us.\n        var hook = globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__;\n        if (hook === undefined) {\n          // However, if there is no DevTools extension, we'll need to set up the global hook ourselves.\n          // Note that in this case it's important that renderer code runs *after* this method call.\n          // Otherwise, the renderer will think that there is no global hook, and won't do the injection.\n          var nextID = 0;\n          globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__ = hook = {\n            renderers: new Map(),\n            supportsFiber: true,\n            inject: function (injected) {\n              return nextID++;\n            },\n            onScheduleFiberRoot: function (id, root, children) {},\n            onCommitFiberRoot: function (id, root, maybePriorityLevel, didError) {},\n            onCommitFiberUnmount: function () {}\n          };\n        }\n        if (hook.isDisabled) {\n          // This isn't a real property on the hook, but it can be set to opt out\n          // of DevTools integration and associated warnings and logs.\n          // Using console['warn'] to evade Babel and ESLint\n          console['warn']('Something has shimmed the React DevTools global hook (__REACT_DEVTOOLS_GLOBAL_HOOK__). ' + 'Fast Refresh is not compatible with this shim and will be disabled.');\n          return;\n        } // Here, we just want to get a reference to scheduleRefresh.\n\n        var oldInject = hook.inject;\n        hook.inject = function (injected) {\n          var id = oldInject.apply(this, arguments);\n          if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n            // This version supports React Refresh.\n            helpersByRendererID.set(id, injected);\n          }\n          return id;\n        }; // Do the same for any already injected roots.\n        // This is useful if ReactDOM has already been initialized.\n        // https://github.com/facebook/react/issues/17626\n\n        hook.renderers.forEach(function (injected, id) {\n          if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n            // This version supports React Refresh.\n            helpersByRendererID.set(id, injected);\n          }\n        }); // We also want to track currently mounted roots.\n\n        var oldOnCommitFiberRoot = hook.onCommitFiberRoot;\n        var oldOnScheduleFiberRoot = hook.onScheduleFiberRoot || function () {};\n        hook.onScheduleFiberRoot = function (id, root, children) {\n          if (!isPerformingRefresh) {\n            // If it was intentionally scheduled, don't attempt to restore.\n            // This includes intentionally scheduled unmounts.\n            failedRoots.delete(root);\n            if (rootElements !== null) {\n              rootElements.set(root, children);\n            }\n          }\n          return oldOnScheduleFiberRoot.apply(this, arguments);\n        };\n        hook.onCommitFiberRoot = function (id, root, maybePriorityLevel, didError) {\n          var helpers = helpersByRendererID.get(id);\n          if (helpers !== undefined) {\n            helpersByRoot.set(root, helpers);\n            var current = root.current;\n            var alternate = current.alternate; // We need to determine whether this root has just (un)mounted.\n            // This logic is copy-pasted from similar logic in the DevTools backend.\n            // If this breaks with some refactoring, you'll want to update DevTools too.\n\n            if (alternate !== null) {\n              var wasMounted = alternate.memoizedState != null && alternate.memoizedState.element != null;\n              var isMounted = current.memoizedState != null && current.memoizedState.element != null;\n              if (!wasMounted && isMounted) {\n                // Mount a new root.\n                mountedRoots.add(root);\n                failedRoots.delete(root);\n              } else if (wasMounted && isMounted) ;else if (wasMounted && !isMounted) {\n                // Unmount an existing root.\n                mountedRoots.delete(root);\n                if (didError) {\n                  // We'll remount it on future edits.\n                  failedRoots.add(root);\n                } else {\n                  helpersByRoot.delete(root);\n                }\n              } else if (!wasMounted && !isMounted) {\n                if (didError) {\n                  // We'll remount it on future edits.\n                  failedRoots.add(root);\n                }\n              }\n            } else {\n              // Mount a new root.\n              mountedRoots.add(root);\n            }\n          } // Always call the decorated DevTools hook.\n\n          return oldOnCommitFiberRoot.apply(this, arguments);\n        };\n      }\n    }\n    function hasUnrecoverableErrors() {\n      // TODO: delete this after removing dependency in RN.\n      return false;\n    } // Exposed for testing.\n\n    function _getMountedRootCount() {\n      {\n        return mountedRoots.size;\n      }\n    } // This is a wrapper over more primitive functions for setting signature.\n    // Signatures let us decide whether the Hook order has changed on refresh.\n    //\n    // This function is intended to be used as a transform target, e.g.:\n    // var _s = createSignatureFunctionForTransform()\n    //\n    // function Hello() {\n    //   const [foo, setFoo] = useState(0);\n    //   const value = useCustomHook();\n    //   _s(); /* Call without arguments triggers collecting the custom Hook list.\n    //          * This doesn't happen during the module evaluation because we\n    //          * don't want to change the module order with inline requires.\n    //          * Next calls are noops. */\n    //   return <h1>Hi</h1>;\n    // }\n    //\n    // /* Call with arguments attaches the signature to the type: */\n    // _s(\n    //   Hello,\n    //   'useState{[foo, setFoo]}(0)',\n    //   () => [useCustomHook], /* Lazy to avoid triggering inline requires */\n    // );\n\n    function createSignatureFunctionForTransform() {\n      {\n        var savedType;\n        var hasCustomHooks;\n        var didCollectHooks = false;\n        return function (type, key, forceReset, getCustomHooks) {\n          if (typeof key === 'string') {\n            // We're in the initial phase that associates signatures\n            // with the functions. Note this may be called multiple times\n            // in HOC chains like _s(hoc1(_s(hoc2(_s(actualFunction))))).\n            if (!savedType) {\n              // We're in the innermost call, so this is the actual type.\n              savedType = type;\n              hasCustomHooks = typeof getCustomHooks === 'function';\n            } // Set the signature for all types (even wrappers!) in case\n            // they have no signatures of their own. This is to prevent\n            // problems like https://github.com/facebook/react/issues/20417.\n\n            if (type != null && (typeof type === 'function' || typeof type === 'object')) {\n              setSignature(type, key, forceReset, getCustomHooks);\n            }\n            return type;\n          } else {\n            // We're in the _s() call without arguments, which means\n            // this is the time to collect custom Hook signatures.\n            // Only do this once. This path is hot and runs *inside* every render!\n            if (!didCollectHooks && hasCustomHooks) {\n              didCollectHooks = true;\n              collectCustomHooksForSignature(savedType);\n            }\n          }\n        };\n      }\n    }\n    function isLikelyComponentType(type) {\n      {\n        switch (typeof type) {\n          case 'function':\n            {\n              // First, deal with classes.\n              if (type.prototype != null) {\n                if (type.prototype.isReactComponent) {\n                  // React class.\n                  return true;\n                }\n                var ownNames = Object.getOwnPropertyNames(type.prototype);\n                if (ownNames.length > 1 || ownNames[0] !== 'constructor') {\n                  // This looks like a class.\n                  return false;\n                } // eslint-disable-next-line no-proto\n\n                if (type.prototype.__proto__ !== Object.prototype) {\n                  // It has a superclass.\n                  return false;\n                } // Pass through.\n                // This looks like a regular function with empty prototype.\n              } // For plain functions and arrows, use name as a heuristic.\n\n              var name = type.name || type.displayName;\n              return typeof name === 'string' && /^[A-Z]/.test(name);\n            }\n          case 'object':\n            {\n              if (type != null) {\n                switch (getProperty(type, '$$typeof')) {\n                  case REACT_FORWARD_REF_TYPE:\n                  case REACT_MEMO_TYPE:\n                    // Definitely React components.\n                    return true;\n                  default:\n                    return false;\n                }\n              }\n              return false;\n            }\n          default:\n            {\n              return false;\n            }\n        }\n      }\n    }\n    exports._getMountedRootCount = _getMountedRootCount;\n    exports.collectCustomHooksForSignature = collectCustomHooksForSignature;\n    exports.createSignatureFunctionForTransform = createSignatureFunctionForTransform;\n    exports.findAffectedHostInstances = findAffectedHostInstances;\n    exports.getFamilyByID = getFamilyByID;\n    exports.getFamilyByType = getFamilyByType;\n    exports.hasUnrecoverableErrors = hasUnrecoverableErrors;\n    exports.injectIntoGlobalHook = injectIntoGlobalHook;\n    exports.isLikelyComponentType = isLikelyComponentType;\n    exports.performReactRefresh = performReactRefresh;\n    exports.register = register;\n    exports.setSignature = setSignature;\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "REACT_ELEMENT_TYPE", "REACT_PORTAL_TYPE", "REACT_FRAGMENT_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_PROVIDER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_SCOPE_TYPE", "REACT_DEBUG_TRACING_MODE_TYPE", "REACT_OFFSCREEN_TYPE", "REACT_LEGACY_HIDDEN_TYPE", "REACT_CACHE_TYPE", "Symbol", "for", "symbolFor", "PossiblyWeakMap", "WeakMap", "Map", "allFamiliesByID", "allFamiliesByType", "allSignaturesByType", "updatedFamiliesByType", "pendingUpdates", "helpersByRendererID", "helpersByRoot", "mountedRoots", "Set", "failedRoots", "rootElements", "isPerformingRefresh", "computeFullKey", "signature", "<PERSON><PERSON><PERSON>", "own<PERSON>ey", "hooks", "getCustomHooks", "err", "forceReset", "i", "length", "hook", "nestedHookSignature", "get", "undefined", "nested<PERSON><PERSON><PERSON><PERSON>", "haveEqualSignatures", "prevType", "nextType", "prevSignature", "nextSignature", "isReactClass", "type", "prototype", "isReactComponent", "canPreserveStateBetween", "resolveFamily", "cloneMap", "map", "clone", "for<PERSON>ach", "value", "key", "set", "cloneSet", "add", "getProperty", "object", "property", "performReactRefresh", "staleFamilies", "updatedFamilies", "updates", "_ref", "family", "current", "update", "helpers", "setRefreshHandler", "<PERSON><PERSON><PERSON><PERSON>", "firstError", "failedRootsSnapshot", "mountedRootsSnapshot", "helpersByRootSnapshot", "root", "Error", "has", "element", "scheduleRoot", "scheduleRefresh", "register", "id", "push", "render", "setSignature", "arguments", "collectCustomHooksForSignature", "getFamilyByID", "getFamilyByType", "findAffectedHostInstances", "families", "affectedInstances", "instancesForRoot", "findHostInstancesForRefresh", "inst", "injectIntoGlobalHook", "globalObject", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "nextID", "renderers", "supportsFiber", "inject", "injected", "onScheduleFiberRoot", "children", "onCommitFiberRoot", "maybePriorityLevel", "onCommitFiberUnmount", "isDisabled", "console", "oldInject", "apply", "oldOnCommitFiberRoot", "oldOnScheduleFiberRoot", "delete", "alternate", "wasMounted", "memoizedState", "isMounted", "hasUnrecoverableErrors", "_getMountedRootCount", "size", "createSignatureFunctionForTransform", "savedType", "hasCustomHooks", "didCollectHooks", "isLikelyComponentType", "ownNames", "Object", "getOwnPropertyNames", "__proto__", "name", "displayName", "test", "exports"], "sources": ["D:/mysql-project/frontend/node_modules/react-refresh/cjs/react-refresh-runtime.development.js"], "sourcesContent": ["/** @license React vundefined\n * react-refresh-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar REACT_ELEMENT_TYPE = 0xeac7;\nvar REACT_PORTAL_TYPE = 0xeaca;\nvar REACT_FRAGMENT_TYPE = 0xeacb;\nvar REACT_STRICT_MODE_TYPE = 0xeacc;\nvar REACT_PROFILER_TYPE = 0xead2;\nvar REACT_PROVIDER_TYPE = 0xeacd;\nvar REACT_CONTEXT_TYPE = 0xeace;\nvar REACT_FORWARD_REF_TYPE = 0xead0;\nvar REACT_SUSPENSE_TYPE = 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = 0xead8;\nvar REACT_MEMO_TYPE = 0xead3;\nvar REACT_LAZY_TYPE = 0xead4;\nvar REACT_SCOPE_TYPE = 0xead7;\nvar REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\nvar REACT_OFFSCREEN_TYPE = 0xeae2;\nvar REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\nvar REACT_CACHE_TYPE = 0xeae4;\n\nif (typeof Symbol === 'function' && Symbol.for) {\n  var symbolFor = Symbol.for;\n  REACT_ELEMENT_TYPE = symbolFor('react.element');\n  REACT_PORTAL_TYPE = symbolFor('react.portal');\n  REACT_FRAGMENT_TYPE = symbolFor('react.fragment');\n  REACT_STRICT_MODE_TYPE = symbolFor('react.strict_mode');\n  REACT_PROFILER_TYPE = symbolFor('react.profiler');\n  REACT_PROVIDER_TYPE = symbolFor('react.provider');\n  REACT_CONTEXT_TYPE = symbolFor('react.context');\n  REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n  REACT_SUSPENSE_TYPE = symbolFor('react.suspense');\n  REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n  REACT_MEMO_TYPE = symbolFor('react.memo');\n  REACT_LAZY_TYPE = symbolFor('react.lazy');\n  REACT_SCOPE_TYPE = symbolFor('react.scope');\n  REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n  REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n  REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n  REACT_CACHE_TYPE = symbolFor('react.cache');\n}\n\nvar PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map; // We never remove these associations.\n// It's OK to reference families, but use WeakMap/Set for types.\n\nvar allFamiliesByID = new Map();\nvar allFamiliesByType = new PossiblyWeakMap();\nvar allSignaturesByType = new PossiblyWeakMap(); // This WeakMap is read by React, so we only put families\n// that have actually been edited here. This keeps checks fast.\n// $FlowIssue\n\nvar updatedFamiliesByType = new PossiblyWeakMap(); // This is cleared on every performReactRefresh() call.\n// It is an array of [Family, NextType] tuples.\n\nvar pendingUpdates = []; // This is injected by the renderer via DevTools global hook.\n\nvar helpersByRendererID = new Map();\nvar helpersByRoot = new Map(); // We keep track of mounted roots so we can schedule updates.\n\nvar mountedRoots = new Set(); // If a root captures an error, we remember it so we can retry on edit.\n\nvar failedRoots = new Set(); // In environments that support WeakMap, we also remember the last element for every root.\n// It needs to be weak because we do this even for roots that failed to mount.\n// If there is no WeakMap, we won't attempt to do retrying.\n// $FlowIssue\n\nvar rootElements = // $FlowIssue\ntypeof WeakMap === 'function' ? new WeakMap() : null;\nvar isPerformingRefresh = false;\n\nfunction computeFullKey(signature) {\n  if (signature.fullKey !== null) {\n    return signature.fullKey;\n  }\n\n  var fullKey = signature.ownKey;\n  var hooks;\n\n  try {\n    hooks = signature.getCustomHooks();\n  } catch (err) {\n    // This can happen in an edge case, e.g. if expression like Foo.useSomething\n    // depends on Foo which is lazily initialized during rendering.\n    // In that case just assume we'll have to remount.\n    signature.forceReset = true;\n    signature.fullKey = fullKey;\n    return fullKey;\n  }\n\n  for (var i = 0; i < hooks.length; i++) {\n    var hook = hooks[i];\n\n    if (typeof hook !== 'function') {\n      // Something's wrong. Assume we need to remount.\n      signature.forceReset = true;\n      signature.fullKey = fullKey;\n      return fullKey;\n    }\n\n    var nestedHookSignature = allSignaturesByType.get(hook);\n\n    if (nestedHookSignature === undefined) {\n      // No signature means Hook wasn't in the source code, e.g. in a library.\n      // We'll skip it because we can assume it won't change during this session.\n      continue;\n    }\n\n    var nestedHookKey = computeFullKey(nestedHookSignature);\n\n    if (nestedHookSignature.forceReset) {\n      signature.forceReset = true;\n    }\n\n    fullKey += '\\n---\\n' + nestedHookKey;\n  }\n\n  signature.fullKey = fullKey;\n  return fullKey;\n}\n\nfunction haveEqualSignatures(prevType, nextType) {\n  var prevSignature = allSignaturesByType.get(prevType);\n  var nextSignature = allSignaturesByType.get(nextType);\n\n  if (prevSignature === undefined && nextSignature === undefined) {\n    return true;\n  }\n\n  if (prevSignature === undefined || nextSignature === undefined) {\n    return false;\n  }\n\n  if (computeFullKey(prevSignature) !== computeFullKey(nextSignature)) {\n    return false;\n  }\n\n  if (nextSignature.forceReset) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction isReactClass(type) {\n  return type.prototype && type.prototype.isReactComponent;\n}\n\nfunction canPreserveStateBetween(prevType, nextType) {\n  if (isReactClass(prevType) || isReactClass(nextType)) {\n    return false;\n  }\n\n  if (haveEqualSignatures(prevType, nextType)) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction resolveFamily(type) {\n  // Only check updated types to keep lookups fast.\n  return updatedFamiliesByType.get(type);\n} // If we didn't care about IE11, we could use new Map/Set(iterable).\n\n\nfunction cloneMap(map) {\n  var clone = new Map();\n  map.forEach(function (value, key) {\n    clone.set(key, value);\n  });\n  return clone;\n}\n\nfunction cloneSet(set) {\n  var clone = new Set();\n  set.forEach(function (value) {\n    clone.add(value);\n  });\n  return clone;\n} // This is a safety mechanism to protect against rogue getters and Proxies.\n\n\nfunction getProperty(object, property) {\n  try {\n    return object[property];\n  } catch (err) {\n    // Intentionally ignore.\n    return undefined;\n  }\n}\n\nfunction performReactRefresh() {\n\n  if (pendingUpdates.length === 0) {\n    return null;\n  }\n\n  if (isPerformingRefresh) {\n    return null;\n  }\n\n  isPerformingRefresh = true;\n\n  try {\n    var staleFamilies = new Set();\n    var updatedFamilies = new Set();\n    var updates = pendingUpdates;\n    pendingUpdates = [];\n    updates.forEach(function (_ref) {\n      var family = _ref[0],\n          nextType = _ref[1];\n      // Now that we got a real edit, we can create associations\n      // that will be read by the React reconciler.\n      var prevType = family.current;\n      updatedFamiliesByType.set(prevType, family);\n      updatedFamiliesByType.set(nextType, family);\n      family.current = nextType; // Determine whether this should be a re-render or a re-mount.\n\n      if (canPreserveStateBetween(prevType, nextType)) {\n        updatedFamilies.add(family);\n      } else {\n        staleFamilies.add(family);\n      }\n    }); // TODO: rename these fields to something more meaningful.\n\n    var update = {\n      updatedFamilies: updatedFamilies,\n      // Families that will re-render preserving state\n      staleFamilies: staleFamilies // Families that will be remounted\n\n    };\n    helpersByRendererID.forEach(function (helpers) {\n      // Even if there are no roots, set the handler on first update.\n      // This ensures that if *new* roots are mounted, they'll use the resolve handler.\n      helpers.setRefreshHandler(resolveFamily);\n    });\n    var didError = false;\n    var firstError = null; // We snapshot maps and sets that are mutated during commits.\n    // If we don't do this, there is a risk they will be mutated while\n    // we iterate over them. For example, trying to recover a failed root\n    // may cause another root to be added to the failed list -- an infinite loop.\n\n    var failedRootsSnapshot = cloneSet(failedRoots);\n    var mountedRootsSnapshot = cloneSet(mountedRoots);\n    var helpersByRootSnapshot = cloneMap(helpersByRoot);\n    failedRootsSnapshot.forEach(function (root) {\n      var helpers = helpersByRootSnapshot.get(root);\n\n      if (helpers === undefined) {\n        throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n      }\n\n      if (!failedRoots.has(root)) {// No longer failed.\n      }\n\n      if (rootElements === null) {\n        return;\n      }\n\n      if (!rootElements.has(root)) {\n        return;\n      }\n\n      var element = rootElements.get(root);\n\n      try {\n        helpers.scheduleRoot(root, element);\n      } catch (err) {\n        if (!didError) {\n          didError = true;\n          firstError = err;\n        } // Keep trying other roots.\n\n      }\n    });\n    mountedRootsSnapshot.forEach(function (root) {\n      var helpers = helpersByRootSnapshot.get(root);\n\n      if (helpers === undefined) {\n        throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n      }\n\n      if (!mountedRoots.has(root)) {// No longer mounted.\n      }\n\n      try {\n        helpers.scheduleRefresh(root, update);\n      } catch (err) {\n        if (!didError) {\n          didError = true;\n          firstError = err;\n        } // Keep trying other roots.\n\n      }\n    });\n\n    if (didError) {\n      throw firstError;\n    }\n\n    return update;\n  } finally {\n    isPerformingRefresh = false;\n  }\n}\nfunction register(type, id) {\n  {\n    if (type === null) {\n      return;\n    }\n\n    if (typeof type !== 'function' && typeof type !== 'object') {\n      return;\n    } // This can happen in an edge case, e.g. if we register\n    // return value of a HOC but it returns a cached component.\n    // Ignore anything but the first registration for each type.\n\n\n    if (allFamiliesByType.has(type)) {\n      return;\n    } // Create family or remember to update it.\n    // None of this bookkeeping affects reconciliation\n    // until the first performReactRefresh() call above.\n\n\n    var family = allFamiliesByID.get(id);\n\n    if (family === undefined) {\n      family = {\n        current: type\n      };\n      allFamiliesByID.set(id, family);\n    } else {\n      pendingUpdates.push([family, type]);\n    }\n\n    allFamiliesByType.set(type, family); // Visit inner types because we might not have registered them.\n\n    if (typeof type === 'object' && type !== null) {\n      switch (getProperty(type, '$$typeof')) {\n        case REACT_FORWARD_REF_TYPE:\n          register(type.render, id + '$render');\n          break;\n\n        case REACT_MEMO_TYPE:\n          register(type.type, id + '$type');\n          break;\n      }\n    }\n  }\n}\nfunction setSignature(type, key) {\n  var forceReset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var getCustomHooks = arguments.length > 3 ? arguments[3] : undefined;\n\n  {\n    if (!allSignaturesByType.has(type)) {\n      allSignaturesByType.set(type, {\n        forceReset: forceReset,\n        ownKey: key,\n        fullKey: null,\n        getCustomHooks: getCustomHooks || function () {\n          return [];\n        }\n      });\n    } // Visit inner types because we might not have signed them.\n\n\n    if (typeof type === 'object' && type !== null) {\n      switch (getProperty(type, '$$typeof')) {\n        case REACT_FORWARD_REF_TYPE:\n          setSignature(type.render, key, forceReset, getCustomHooks);\n          break;\n\n        case REACT_MEMO_TYPE:\n          setSignature(type.type, key, forceReset, getCustomHooks);\n          break;\n      }\n    }\n  }\n} // This is lazily called during first render for a type.\n// It captures Hook list at that time so inline requires don't break comparisons.\n\nfunction collectCustomHooksForSignature(type) {\n  {\n    var signature = allSignaturesByType.get(type);\n\n    if (signature !== undefined) {\n      computeFullKey(signature);\n    }\n  }\n}\nfunction getFamilyByID(id) {\n  {\n    return allFamiliesByID.get(id);\n  }\n}\nfunction getFamilyByType(type) {\n  {\n    return allFamiliesByType.get(type);\n  }\n}\nfunction findAffectedHostInstances(families) {\n  {\n    var affectedInstances = new Set();\n    mountedRoots.forEach(function (root) {\n      var helpers = helpersByRoot.get(root);\n\n      if (helpers === undefined) {\n        throw new Error('Could not find helpers for a root. This is a bug in React Refresh.');\n      }\n\n      var instancesForRoot = helpers.findHostInstancesForRefresh(root, families);\n      instancesForRoot.forEach(function (inst) {\n        affectedInstances.add(inst);\n      });\n    });\n    return affectedInstances;\n  }\n}\nfunction injectIntoGlobalHook(globalObject) {\n  {\n    // For React Native, the global hook will be set up by require('react-devtools-core').\n    // That code will run before us. So we need to monkeypatch functions on existing hook.\n    // For React Web, the global hook will be set up by the extension.\n    // This will also run before us.\n    var hook = globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__;\n\n    if (hook === undefined) {\n      // However, if there is no DevTools extension, we'll need to set up the global hook ourselves.\n      // Note that in this case it's important that renderer code runs *after* this method call.\n      // Otherwise, the renderer will think that there is no global hook, and won't do the injection.\n      var nextID = 0;\n      globalObject.__REACT_DEVTOOLS_GLOBAL_HOOK__ = hook = {\n        renderers: new Map(),\n        supportsFiber: true,\n        inject: function (injected) {\n          return nextID++;\n        },\n        onScheduleFiberRoot: function (id, root, children) {},\n        onCommitFiberRoot: function (id, root, maybePriorityLevel, didError) {},\n        onCommitFiberUnmount: function () {}\n      };\n    }\n\n    if (hook.isDisabled) {\n      // This isn't a real property on the hook, but it can be set to opt out\n      // of DevTools integration and associated warnings and logs.\n      // Using console['warn'] to evade Babel and ESLint\n      console['warn']('Something has shimmed the React DevTools global hook (__REACT_DEVTOOLS_GLOBAL_HOOK__). ' + 'Fast Refresh is not compatible with this shim and will be disabled.');\n      return;\n    } // Here, we just want to get a reference to scheduleRefresh.\n\n\n    var oldInject = hook.inject;\n\n    hook.inject = function (injected) {\n      var id = oldInject.apply(this, arguments);\n\n      if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n        // This version supports React Refresh.\n        helpersByRendererID.set(id, injected);\n      }\n\n      return id;\n    }; // Do the same for any already injected roots.\n    // This is useful if ReactDOM has already been initialized.\n    // https://github.com/facebook/react/issues/17626\n\n\n    hook.renderers.forEach(function (injected, id) {\n      if (typeof injected.scheduleRefresh === 'function' && typeof injected.setRefreshHandler === 'function') {\n        // This version supports React Refresh.\n        helpersByRendererID.set(id, injected);\n      }\n    }); // We also want to track currently mounted roots.\n\n    var oldOnCommitFiberRoot = hook.onCommitFiberRoot;\n\n    var oldOnScheduleFiberRoot = hook.onScheduleFiberRoot || function () {};\n\n    hook.onScheduleFiberRoot = function (id, root, children) {\n      if (!isPerformingRefresh) {\n        // If it was intentionally scheduled, don't attempt to restore.\n        // This includes intentionally scheduled unmounts.\n        failedRoots.delete(root);\n\n        if (rootElements !== null) {\n          rootElements.set(root, children);\n        }\n      }\n\n      return oldOnScheduleFiberRoot.apply(this, arguments);\n    };\n\n    hook.onCommitFiberRoot = function (id, root, maybePriorityLevel, didError) {\n      var helpers = helpersByRendererID.get(id);\n\n      if (helpers !== undefined) {\n        helpersByRoot.set(root, helpers);\n        var current = root.current;\n        var alternate = current.alternate; // We need to determine whether this root has just (un)mounted.\n        // This logic is copy-pasted from similar logic in the DevTools backend.\n        // If this breaks with some refactoring, you'll want to update DevTools too.\n\n        if (alternate !== null) {\n          var wasMounted = alternate.memoizedState != null && alternate.memoizedState.element != null;\n          var isMounted = current.memoizedState != null && current.memoizedState.element != null;\n\n          if (!wasMounted && isMounted) {\n            // Mount a new root.\n            mountedRoots.add(root);\n            failedRoots.delete(root);\n          } else if (wasMounted && isMounted) ; else if (wasMounted && !isMounted) {\n            // Unmount an existing root.\n            mountedRoots.delete(root);\n\n            if (didError) {\n              // We'll remount it on future edits.\n              failedRoots.add(root);\n            } else {\n              helpersByRoot.delete(root);\n            }\n          } else if (!wasMounted && !isMounted) {\n            if (didError) {\n              // We'll remount it on future edits.\n              failedRoots.add(root);\n            }\n          }\n        } else {\n          // Mount a new root.\n          mountedRoots.add(root);\n        }\n      } // Always call the decorated DevTools hook.\n\n\n      return oldOnCommitFiberRoot.apply(this, arguments);\n    };\n  }\n}\nfunction hasUnrecoverableErrors() {\n  // TODO: delete this after removing dependency in RN.\n  return false;\n} // Exposed for testing.\n\nfunction _getMountedRootCount() {\n  {\n    return mountedRoots.size;\n  }\n} // This is a wrapper over more primitive functions for setting signature.\n// Signatures let us decide whether the Hook order has changed on refresh.\n//\n// This function is intended to be used as a transform target, e.g.:\n// var _s = createSignatureFunctionForTransform()\n//\n// function Hello() {\n//   const [foo, setFoo] = useState(0);\n//   const value = useCustomHook();\n//   _s(); /* Call without arguments triggers collecting the custom Hook list.\n//          * This doesn't happen during the module evaluation because we\n//          * don't want to change the module order with inline requires.\n//          * Next calls are noops. */\n//   return <h1>Hi</h1>;\n// }\n//\n// /* Call with arguments attaches the signature to the type: */\n// _s(\n//   Hello,\n//   'useState{[foo, setFoo]}(0)',\n//   () => [useCustomHook], /* Lazy to avoid triggering inline requires */\n// );\n\nfunction createSignatureFunctionForTransform() {\n  {\n    var savedType;\n    var hasCustomHooks;\n    var didCollectHooks = false;\n    return function (type, key, forceReset, getCustomHooks) {\n      if (typeof key === 'string') {\n        // We're in the initial phase that associates signatures\n        // with the functions. Note this may be called multiple times\n        // in HOC chains like _s(hoc1(_s(hoc2(_s(actualFunction))))).\n        if (!savedType) {\n          // We're in the innermost call, so this is the actual type.\n          savedType = type;\n          hasCustomHooks = typeof getCustomHooks === 'function';\n        } // Set the signature for all types (even wrappers!) in case\n        // they have no signatures of their own. This is to prevent\n        // problems like https://github.com/facebook/react/issues/20417.\n\n\n        if (type != null && (typeof type === 'function' || typeof type === 'object')) {\n          setSignature(type, key, forceReset, getCustomHooks);\n        }\n\n        return type;\n      } else {\n        // We're in the _s() call without arguments, which means\n        // this is the time to collect custom Hook signatures.\n        // Only do this once. This path is hot and runs *inside* every render!\n        if (!didCollectHooks && hasCustomHooks) {\n          didCollectHooks = true;\n          collectCustomHooksForSignature(savedType);\n        }\n      }\n    };\n  }\n}\nfunction isLikelyComponentType(type) {\n  {\n    switch (typeof type) {\n      case 'function':\n        {\n          // First, deal with classes.\n          if (type.prototype != null) {\n            if (type.prototype.isReactComponent) {\n              // React class.\n              return true;\n            }\n\n            var ownNames = Object.getOwnPropertyNames(type.prototype);\n\n            if (ownNames.length > 1 || ownNames[0] !== 'constructor') {\n              // This looks like a class.\n              return false;\n            } // eslint-disable-next-line no-proto\n\n\n            if (type.prototype.__proto__ !== Object.prototype) {\n              // It has a superclass.\n              return false;\n            } // Pass through.\n            // This looks like a regular function with empty prototype.\n\n          } // For plain functions and arrows, use name as a heuristic.\n\n\n          var name = type.name || type.displayName;\n          return typeof name === 'string' && /^[A-Z]/.test(name);\n        }\n\n      case 'object':\n        {\n          if (type != null) {\n            switch (getProperty(type, '$$typeof')) {\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_MEMO_TYPE:\n                // Definitely React components.\n                return true;\n\n              default:\n                return false;\n            }\n          }\n\n          return false;\n        }\n\n      default:\n        {\n          return false;\n        }\n    }\n  }\n}\n\nexports._getMountedRootCount = _getMountedRootCount;\nexports.collectCustomHooksForSignature = collectCustomHooksForSignature;\nexports.createSignatureFunctionForTransform = createSignatureFunctionForTransform;\nexports.findAffectedHostInstances = findAffectedHostInstances;\nexports.getFamilyByID = getFamilyByID;\nexports.getFamilyByType = getFamilyByType;\nexports.hasUnrecoverableErrors = hasUnrecoverableErrors;\nexports.injectIntoGlobalHook = injectIntoGlobalHook;\nexports.isLikelyComponentType = isLikelyComponentType;\nexports.performReactRefresh = performReactRefresh;\nexports.register = register;\nexports.setSignature = setSignature;\n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,CAAC,YAAW;IACd,YAAY;;IAEZ;IACA;IACA;IACA;IACA;IACA,IAAIC,kBAAkB,GAAG,MAAM;IAC/B,IAAIC,iBAAiB,GAAG,MAAM;IAC9B,IAAIC,mBAAmB,GAAG,MAAM;IAChC,IAAIC,sBAAsB,GAAG,MAAM;IACnC,IAAIC,mBAAmB,GAAG,MAAM;IAChC,IAAIC,mBAAmB,GAAG,MAAM;IAChC,IAAIC,kBAAkB,GAAG,MAAM;IAC/B,IAAIC,sBAAsB,GAAG,MAAM;IACnC,IAAIC,mBAAmB,GAAG,MAAM;IAChC,IAAIC,wBAAwB,GAAG,MAAM;IACrC,IAAIC,eAAe,GAAG,MAAM;IAC5B,IAAIC,eAAe,GAAG,MAAM;IAC5B,IAAIC,gBAAgB,GAAG,MAAM;IAC7B,IAAIC,6BAA6B,GAAG,MAAM;IAC1C,IAAIC,oBAAoB,GAAG,MAAM;IACjC,IAAIC,wBAAwB,GAAG,MAAM;IACrC,IAAIC,gBAAgB,GAAG,MAAM;IAE7B,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;MAC9C,IAAIC,SAAS,GAAGF,MAAM,CAACC,GAAG;MAC1BlB,kBAAkB,GAAGmB,SAAS,CAAC,eAAe,CAAC;MAC/ClB,iBAAiB,GAAGkB,SAAS,CAAC,cAAc,CAAC;MAC7CjB,mBAAmB,GAAGiB,SAAS,CAAC,gBAAgB,CAAC;MACjDhB,sBAAsB,GAAGgB,SAAS,CAAC,mBAAmB,CAAC;MACvDf,mBAAmB,GAAGe,SAAS,CAAC,gBAAgB,CAAC;MACjDd,mBAAmB,GAAGc,SAAS,CAAC,gBAAgB,CAAC;MACjDb,kBAAkB,GAAGa,SAAS,CAAC,eAAe,CAAC;MAC/CZ,sBAAsB,GAAGY,SAAS,CAAC,mBAAmB,CAAC;MACvDX,mBAAmB,GAAGW,SAAS,CAAC,gBAAgB,CAAC;MACjDV,wBAAwB,GAAGU,SAAS,CAAC,qBAAqB,CAAC;MAC3DT,eAAe,GAAGS,SAAS,CAAC,YAAY,CAAC;MACzCR,eAAe,GAAGQ,SAAS,CAAC,YAAY,CAAC;MACzCP,gBAAgB,GAAGO,SAAS,CAAC,aAAa,CAAC;MAC3CN,6BAA6B,GAAGM,SAAS,CAAC,wBAAwB,CAAC;MACnEL,oBAAoB,GAAGK,SAAS,CAAC,iBAAiB,CAAC;MACnDJ,wBAAwB,GAAGI,SAAS,CAAC,qBAAqB,CAAC;MAC3DH,gBAAgB,GAAGG,SAAS,CAAC,aAAa,CAAC;IAC7C;IAEA,IAAIC,eAAe,GAAG,OAAOC,OAAO,KAAK,UAAU,GAAGA,OAAO,GAAGC,GAAG,CAAC,CAAC;IACrE;;IAEA,IAAIC,eAAe,GAAG,IAAID,GAAG,CAAC,CAAC;IAC/B,IAAIE,iBAAiB,GAAG,IAAIJ,eAAe,CAAC,CAAC;IAC7C,IAAIK,mBAAmB,GAAG,IAAIL,eAAe,CAAC,CAAC,CAAC,CAAC;IACjD;IACA;;IAEA,IAAIM,qBAAqB,GAAG,IAAIN,eAAe,CAAC,CAAC,CAAC,CAAC;IACnD;;IAEA,IAAIO,cAAc,GAAG,EAAE,CAAC,CAAC;;IAEzB,IAAIC,mBAAmB,GAAG,IAAIN,GAAG,CAAC,CAAC;IACnC,IAAIO,aAAa,GAAG,IAAIP,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE/B,IAAIQ,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE9B,IAAIC,WAAW,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7B;IACA;IACA;;IAEA,IAAIE,YAAY;IAAG;IACnB,OAAOZ,OAAO,KAAK,UAAU,GAAG,IAAIA,OAAO,CAAC,CAAC,GAAG,IAAI;IACpD,IAAIa,mBAAmB,GAAG,KAAK;IAE/B,SAASC,cAAcA,CAACC,SAAS,EAAE;MACjC,IAAIA,SAAS,CAACC,OAAO,KAAK,IAAI,EAAE;QAC9B,OAAOD,SAAS,CAACC,OAAO;MAC1B;MAEA,IAAIA,OAAO,GAAGD,SAAS,CAACE,MAAM;MAC9B,IAAIC,KAAK;MAET,IAAI;QACFA,KAAK,GAAGH,SAAS,CAACI,cAAc,CAAC,CAAC;MACpC,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZ;QACA;QACA;QACAL,SAAS,CAACM,UAAU,GAAG,IAAI;QAC3BN,SAAS,CAACC,OAAO,GAAGA,OAAO;QAC3B,OAAOA,OAAO;MAChB;MAEA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,IAAIE,IAAI,GAAGN,KAAK,CAACI,CAAC,CAAC;QAEnB,IAAI,OAAOE,IAAI,KAAK,UAAU,EAAE;UAC9B;UACAT,SAAS,CAACM,UAAU,GAAG,IAAI;UAC3BN,SAAS,CAACC,OAAO,GAAGA,OAAO;UAC3B,OAAOA,OAAO;QAChB;QAEA,IAAIS,mBAAmB,GAAGrB,mBAAmB,CAACsB,GAAG,CAACF,IAAI,CAAC;QAEvD,IAAIC,mBAAmB,KAAKE,SAAS,EAAE;UACrC;UACA;UACA;QACF;QAEA,IAAIC,aAAa,GAAGd,cAAc,CAACW,mBAAmB,CAAC;QAEvD,IAAIA,mBAAmB,CAACJ,UAAU,EAAE;UAClCN,SAAS,CAACM,UAAU,GAAG,IAAI;QAC7B;QAEAL,OAAO,IAAI,SAAS,GAAGY,aAAa;MACtC;MAEAb,SAAS,CAACC,OAAO,GAAGA,OAAO;MAC3B,OAAOA,OAAO;IAChB;IAEA,SAASa,mBAAmBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;MAC/C,IAAIC,aAAa,GAAG5B,mBAAmB,CAACsB,GAAG,CAACI,QAAQ,CAAC;MACrD,IAAIG,aAAa,GAAG7B,mBAAmB,CAACsB,GAAG,CAACK,QAAQ,CAAC;MAErD,IAAIC,aAAa,KAAKL,SAAS,IAAIM,aAAa,KAAKN,SAAS,EAAE;QAC9D,OAAO,IAAI;MACb;MAEA,IAAIK,aAAa,KAAKL,SAAS,IAAIM,aAAa,KAAKN,SAAS,EAAE;QAC9D,OAAO,KAAK;MACd;MAEA,IAAIb,cAAc,CAACkB,aAAa,CAAC,KAAKlB,cAAc,CAACmB,aAAa,CAAC,EAAE;QACnE,OAAO,KAAK;MACd;MAEA,IAAIA,aAAa,CAACZ,UAAU,EAAE;QAC5B,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb;IAEA,SAASa,YAAYA,CAACC,IAAI,EAAE;MAC1B,OAAOA,IAAI,CAACC,SAAS,IAAID,IAAI,CAACC,SAAS,CAACC,gBAAgB;IAC1D;IAEA,SAASC,uBAAuBA,CAACR,QAAQ,EAAEC,QAAQ,EAAE;MACnD,IAAIG,YAAY,CAACJ,QAAQ,CAAC,IAAII,YAAY,CAACH,QAAQ,CAAC,EAAE;QACpD,OAAO,KAAK;MACd;MAEA,IAAIF,mBAAmB,CAACC,QAAQ,EAAEC,QAAQ,CAAC,EAAE;QAC3C,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd;IAEA,SAASQ,aAAaA,CAACJ,IAAI,EAAE;MAC3B;MACA,OAAO9B,qBAAqB,CAACqB,GAAG,CAACS,IAAI,CAAC;IACxC,CAAC,CAAC;;IAGF,SAASK,QAAQA,CAACC,GAAG,EAAE;MACrB,IAAIC,KAAK,GAAG,IAAIzC,GAAG,CAAC,CAAC;MACrBwC,GAAG,CAACE,OAAO,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;QAChCH,KAAK,CAACI,GAAG,CAACD,GAAG,EAAED,KAAK,CAAC;MACvB,CAAC,CAAC;MACF,OAAOF,KAAK;IACd;IAEA,SAASK,QAAQA,CAACD,GAAG,EAAE;MACrB,IAAIJ,KAAK,GAAG,IAAIhC,GAAG,CAAC,CAAC;MACrBoC,GAAG,CAACH,OAAO,CAAC,UAAUC,KAAK,EAAE;QAC3BF,KAAK,CAACM,GAAG,CAACJ,KAAK,CAAC;MAClB,CAAC,CAAC;MACF,OAAOF,KAAK;IACd,CAAC,CAAC;;IAGF,SAASO,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAE;MACrC,IAAI;QACF,OAAOD,MAAM,CAACC,QAAQ,CAAC;MACzB,CAAC,CAAC,OAAO/B,GAAG,EAAE;QACZ;QACA,OAAOO,SAAS;MAClB;IACF;IAEA,SAASyB,mBAAmBA,CAAA,EAAG;MAE7B,IAAI9C,cAAc,CAACiB,MAAM,KAAK,CAAC,EAAE;QAC/B,OAAO,IAAI;MACb;MAEA,IAAIV,mBAAmB,EAAE;QACvB,OAAO,IAAI;MACb;MAEAA,mBAAmB,GAAG,IAAI;MAE1B,IAAI;QACF,IAAIwC,aAAa,GAAG,IAAI3C,GAAG,CAAC,CAAC;QAC7B,IAAI4C,eAAe,GAAG,IAAI5C,GAAG,CAAC,CAAC;QAC/B,IAAI6C,OAAO,GAAGjD,cAAc;QAC5BA,cAAc,GAAG,EAAE;QACnBiD,OAAO,CAACZ,OAAO,CAAC,UAAUa,IAAI,EAAE;UAC9B,IAAIC,MAAM,GAAGD,IAAI,CAAC,CAAC,CAAC;YAChBzB,QAAQ,GAAGyB,IAAI,CAAC,CAAC,CAAC;UACtB;UACA;UACA,IAAI1B,QAAQ,GAAG2B,MAAM,CAACC,OAAO;UAC7BrD,qBAAqB,CAACyC,GAAG,CAAChB,QAAQ,EAAE2B,MAAM,CAAC;UAC3CpD,qBAAqB,CAACyC,GAAG,CAACf,QAAQ,EAAE0B,MAAM,CAAC;UAC3CA,MAAM,CAACC,OAAO,GAAG3B,QAAQ,CAAC,CAAC;;UAE3B,IAAIO,uBAAuB,CAACR,QAAQ,EAAEC,QAAQ,CAAC,EAAE;YAC/CuB,eAAe,CAACN,GAAG,CAACS,MAAM,CAAC;UAC7B,CAAC,MAAM;YACLJ,aAAa,CAACL,GAAG,CAACS,MAAM,CAAC;UAC3B;QACF,CAAC,CAAC,CAAC,CAAC;;QAEJ,IAAIE,MAAM,GAAG;UACXL,eAAe,EAAEA,eAAe;UAChC;UACAD,aAAa,EAAEA,aAAa,CAAC;QAE/B,CAAC;QACD9C,mBAAmB,CAACoC,OAAO,CAAC,UAAUiB,OAAO,EAAE;UAC7C;UACA;UACAA,OAAO,CAACC,iBAAiB,CAACtB,aAAa,CAAC;QAC1C,CAAC,CAAC;QACF,IAAIuB,QAAQ,GAAG,KAAK;QACpB,IAAIC,UAAU,GAAG,IAAI,CAAC,CAAC;QACvB;QACA;QACA;;QAEA,IAAIC,mBAAmB,GAAGjB,QAAQ,CAACpC,WAAW,CAAC;QAC/C,IAAIsD,oBAAoB,GAAGlB,QAAQ,CAACtC,YAAY,CAAC;QACjD,IAAIyD,qBAAqB,GAAG1B,QAAQ,CAAChC,aAAa,CAAC;QACnDwD,mBAAmB,CAACrB,OAAO,CAAC,UAAUwB,IAAI,EAAE;UAC1C,IAAIP,OAAO,GAAGM,qBAAqB,CAACxC,GAAG,CAACyC,IAAI,CAAC;UAE7C,IAAIP,OAAO,KAAKjC,SAAS,EAAE;YACzB,MAAM,IAAIyC,KAAK,CAAC,oEAAoE,CAAC;UACvF;UAEA,IAAI,CAACzD,WAAW,CAAC0D,GAAG,CAACF,IAAI,CAAC,EAAE,CAAC;UAAA;UAG7B,IAAIvD,YAAY,KAAK,IAAI,EAAE;YACzB;UACF;UAEA,IAAI,CAACA,YAAY,CAACyD,GAAG,CAACF,IAAI,CAAC,EAAE;YAC3B;UACF;UAEA,IAAIG,OAAO,GAAG1D,YAAY,CAACc,GAAG,CAACyC,IAAI,CAAC;UAEpC,IAAI;YACFP,OAAO,CAACW,YAAY,CAACJ,IAAI,EAAEG,OAAO,CAAC;UACrC,CAAC,CAAC,OAAOlD,GAAG,EAAE;YACZ,IAAI,CAAC0C,QAAQ,EAAE;cACbA,QAAQ,GAAG,IAAI;cACfC,UAAU,GAAG3C,GAAG;YAClB,CAAC,CAAC;UAEJ;QACF,CAAC,CAAC;QACF6C,oBAAoB,CAACtB,OAAO,CAAC,UAAUwB,IAAI,EAAE;UAC3C,IAAIP,OAAO,GAAGM,qBAAqB,CAACxC,GAAG,CAACyC,IAAI,CAAC;UAE7C,IAAIP,OAAO,KAAKjC,SAAS,EAAE;YACzB,MAAM,IAAIyC,KAAK,CAAC,oEAAoE,CAAC;UACvF;UAEA,IAAI,CAAC3D,YAAY,CAAC4D,GAAG,CAACF,IAAI,CAAC,EAAE,CAAC;UAAA;UAG9B,IAAI;YACFP,OAAO,CAACY,eAAe,CAACL,IAAI,EAAER,MAAM,CAAC;UACvC,CAAC,CAAC,OAAOvC,GAAG,EAAE;YACZ,IAAI,CAAC0C,QAAQ,EAAE;cACbA,QAAQ,GAAG,IAAI;cACfC,UAAU,GAAG3C,GAAG;YAClB,CAAC,CAAC;UAEJ;QACF,CAAC,CAAC;QAEF,IAAI0C,QAAQ,EAAE;UACZ,MAAMC,UAAU;QAClB;QAEA,OAAOJ,MAAM;MACf,CAAC,SAAS;QACR9C,mBAAmB,GAAG,KAAK;MAC7B;IACF;IACA,SAAS4D,QAAQA,CAACtC,IAAI,EAAEuC,EAAE,EAAE;MAC1B;QACE,IAAIvC,IAAI,KAAK,IAAI,EAAE;UACjB;QACF;QAEA,IAAI,OAAOA,IAAI,KAAK,UAAU,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC1D;QACF,CAAC,CAAC;QACF;QACA;;QAGA,IAAIhC,iBAAiB,CAACkE,GAAG,CAAClC,IAAI,CAAC,EAAE;UAC/B;QACF,CAAC,CAAC;QACF;QACA;;QAGA,IAAIsB,MAAM,GAAGvD,eAAe,CAACwB,GAAG,CAACgD,EAAE,CAAC;QAEpC,IAAIjB,MAAM,KAAK9B,SAAS,EAAE;UACxB8B,MAAM,GAAG;YACPC,OAAO,EAAEvB;UACX,CAAC;UACDjC,eAAe,CAAC4C,GAAG,CAAC4B,EAAE,EAAEjB,MAAM,CAAC;QACjC,CAAC,MAAM;UACLnD,cAAc,CAACqE,IAAI,CAAC,CAAClB,MAAM,EAAEtB,IAAI,CAAC,CAAC;QACrC;QAEAhC,iBAAiB,CAAC2C,GAAG,CAACX,IAAI,EAAEsB,MAAM,CAAC,CAAC,CAAC;;QAErC,IAAI,OAAOtB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;UAC7C,QAAQc,WAAW,CAACd,IAAI,EAAE,UAAU,CAAC;YACnC,KAAKjD,sBAAsB;cACzBuF,QAAQ,CAACtC,IAAI,CAACyC,MAAM,EAAEF,EAAE,GAAG,SAAS,CAAC;cACrC;YAEF,KAAKrF,eAAe;cAClBoF,QAAQ,CAACtC,IAAI,CAACA,IAAI,EAAEuC,EAAE,GAAG,OAAO,CAAC;cACjC;UACJ;QACF;MACF;IACF;IACA,SAASG,YAAYA,CAAC1C,IAAI,EAAEU,GAAG,EAAE;MAC/B,IAAIxB,UAAU,GAAGyD,SAAS,CAACvD,MAAM,GAAG,CAAC,IAAIuD,SAAS,CAAC,CAAC,CAAC,KAAKnD,SAAS,GAAGmD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC1F,IAAI3D,cAAc,GAAG2D,SAAS,CAACvD,MAAM,GAAG,CAAC,GAAGuD,SAAS,CAAC,CAAC,CAAC,GAAGnD,SAAS;MAEpE;QACE,IAAI,CAACvB,mBAAmB,CAACiE,GAAG,CAAClC,IAAI,CAAC,EAAE;UAClC/B,mBAAmB,CAAC0C,GAAG,CAACX,IAAI,EAAE;YAC5Bd,UAAU,EAAEA,UAAU;YACtBJ,MAAM,EAAE4B,GAAG;YACX7B,OAAO,EAAE,IAAI;YACbG,cAAc,EAAEA,cAAc,IAAI,YAAY;cAC5C,OAAO,EAAE;YACX;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;;QAGF,IAAI,OAAOgB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;UAC7C,QAAQc,WAAW,CAACd,IAAI,EAAE,UAAU,CAAC;YACnC,KAAKjD,sBAAsB;cACzB2F,YAAY,CAAC1C,IAAI,CAACyC,MAAM,EAAE/B,GAAG,EAAExB,UAAU,EAAEF,cAAc,CAAC;cAC1D;YAEF,KAAK9B,eAAe;cAClBwF,YAAY,CAAC1C,IAAI,CAACA,IAAI,EAAEU,GAAG,EAAExB,UAAU,EAAEF,cAAc,CAAC;cACxD;UACJ;QACF;MACF;IACF,CAAC,CAAC;IACF;;IAEA,SAAS4D,8BAA8BA,CAAC5C,IAAI,EAAE;MAC5C;QACE,IAAIpB,SAAS,GAAGX,mBAAmB,CAACsB,GAAG,CAACS,IAAI,CAAC;QAE7C,IAAIpB,SAAS,KAAKY,SAAS,EAAE;UAC3Bb,cAAc,CAACC,SAAS,CAAC;QAC3B;MACF;IACF;IACA,SAASiE,aAAaA,CAACN,EAAE,EAAE;MACzB;QACE,OAAOxE,eAAe,CAACwB,GAAG,CAACgD,EAAE,CAAC;MAChC;IACF;IACA,SAASO,eAAeA,CAAC9C,IAAI,EAAE;MAC7B;QACE,OAAOhC,iBAAiB,CAACuB,GAAG,CAACS,IAAI,CAAC;MACpC;IACF;IACA,SAAS+C,yBAAyBA,CAACC,QAAQ,EAAE;MAC3C;QACE,IAAIC,iBAAiB,GAAG,IAAI1E,GAAG,CAAC,CAAC;QACjCD,YAAY,CAACkC,OAAO,CAAC,UAAUwB,IAAI,EAAE;UACnC,IAAIP,OAAO,GAAGpD,aAAa,CAACkB,GAAG,CAACyC,IAAI,CAAC;UAErC,IAAIP,OAAO,KAAKjC,SAAS,EAAE;YACzB,MAAM,IAAIyC,KAAK,CAAC,oEAAoE,CAAC;UACvF;UAEA,IAAIiB,gBAAgB,GAAGzB,OAAO,CAAC0B,2BAA2B,CAACnB,IAAI,EAAEgB,QAAQ,CAAC;UAC1EE,gBAAgB,CAAC1C,OAAO,CAAC,UAAU4C,IAAI,EAAE;YACvCH,iBAAiB,CAACpC,GAAG,CAACuC,IAAI,CAAC;UAC7B,CAAC,CAAC;QACJ,CAAC,CAAC;QACF,OAAOH,iBAAiB;MAC1B;IACF;IACA,SAASI,oBAAoBA,CAACC,YAAY,EAAE;MAC1C;QACE;QACA;QACA;QACA;QACA,IAAIjE,IAAI,GAAGiE,YAAY,CAACC,8BAA8B;QAEtD,IAAIlE,IAAI,KAAKG,SAAS,EAAE;UACtB;UACA;UACA;UACA,IAAIgE,MAAM,GAAG,CAAC;UACdF,YAAY,CAACC,8BAA8B,GAAGlE,IAAI,GAAG;YACnDoE,SAAS,EAAE,IAAI3F,GAAG,CAAC,CAAC;YACpB4F,aAAa,EAAE,IAAI;YACnBC,MAAM,EAAE,SAAAA,CAAUC,QAAQ,EAAE;cAC1B,OAAOJ,MAAM,EAAE;YACjB,CAAC;YACDK,mBAAmB,EAAE,SAAAA,CAAUtB,EAAE,EAAEP,IAAI,EAAE8B,QAAQ,EAAE,CAAC,CAAC;YACrDC,iBAAiB,EAAE,SAAAA,CAAUxB,EAAE,EAAEP,IAAI,EAAEgC,kBAAkB,EAAErC,QAAQ,EAAE,CAAC,CAAC;YACvEsC,oBAAoB,EAAE,SAAAA,CAAA,EAAY,CAAC;UACrC,CAAC;QACH;QAEA,IAAI5E,IAAI,CAAC6E,UAAU,EAAE;UACnB;UACA;UACA;UACAC,OAAO,CAAC,MAAM,CAAC,CAAC,yFAAyF,GAAG,qEAAqE,CAAC;UAClL;QACF,CAAC,CAAC;;QAGF,IAAIC,SAAS,GAAG/E,IAAI,CAACsE,MAAM;QAE3BtE,IAAI,CAACsE,MAAM,GAAG,UAAUC,QAAQ,EAAE;UAChC,IAAIrB,EAAE,GAAG6B,SAAS,CAACC,KAAK,CAAC,IAAI,EAAE1B,SAAS,CAAC;UAEzC,IAAI,OAAOiB,QAAQ,CAACvB,eAAe,KAAK,UAAU,IAAI,OAAOuB,QAAQ,CAAClC,iBAAiB,KAAK,UAAU,EAAE;YACtG;YACAtD,mBAAmB,CAACuC,GAAG,CAAC4B,EAAE,EAAEqB,QAAQ,CAAC;UACvC;UAEA,OAAOrB,EAAE;QACX,CAAC,CAAC,CAAC;QACH;QACA;;QAGAlD,IAAI,CAACoE,SAAS,CAACjD,OAAO,CAAC,UAAUoD,QAAQ,EAAErB,EAAE,EAAE;UAC7C,IAAI,OAAOqB,QAAQ,CAACvB,eAAe,KAAK,UAAU,IAAI,OAAOuB,QAAQ,CAAClC,iBAAiB,KAAK,UAAU,EAAE;YACtG;YACAtD,mBAAmB,CAACuC,GAAG,CAAC4B,EAAE,EAAEqB,QAAQ,CAAC;UACvC;QACF,CAAC,CAAC,CAAC,CAAC;;QAEJ,IAAIU,oBAAoB,GAAGjF,IAAI,CAAC0E,iBAAiB;QAEjD,IAAIQ,sBAAsB,GAAGlF,IAAI,CAACwE,mBAAmB,IAAI,YAAY,CAAC,CAAC;QAEvExE,IAAI,CAACwE,mBAAmB,GAAG,UAAUtB,EAAE,EAAEP,IAAI,EAAE8B,QAAQ,EAAE;UACvD,IAAI,CAACpF,mBAAmB,EAAE;YACxB;YACA;YACAF,WAAW,CAACgG,MAAM,CAACxC,IAAI,CAAC;YAExB,IAAIvD,YAAY,KAAK,IAAI,EAAE;cACzBA,YAAY,CAACkC,GAAG,CAACqB,IAAI,EAAE8B,QAAQ,CAAC;YAClC;UACF;UAEA,OAAOS,sBAAsB,CAACF,KAAK,CAAC,IAAI,EAAE1B,SAAS,CAAC;QACtD,CAAC;QAEDtD,IAAI,CAAC0E,iBAAiB,GAAG,UAAUxB,EAAE,EAAEP,IAAI,EAAEgC,kBAAkB,EAAErC,QAAQ,EAAE;UACzE,IAAIF,OAAO,GAAGrD,mBAAmB,CAACmB,GAAG,CAACgD,EAAE,CAAC;UAEzC,IAAId,OAAO,KAAKjC,SAAS,EAAE;YACzBnB,aAAa,CAACsC,GAAG,CAACqB,IAAI,EAAEP,OAAO,CAAC;YAChC,IAAIF,OAAO,GAAGS,IAAI,CAACT,OAAO;YAC1B,IAAIkD,SAAS,GAAGlD,OAAO,CAACkD,SAAS,CAAC,CAAC;YACnC;YACA;;YAEA,IAAIA,SAAS,KAAK,IAAI,EAAE;cACtB,IAAIC,UAAU,GAAGD,SAAS,CAACE,aAAa,IAAI,IAAI,IAAIF,SAAS,CAACE,aAAa,CAACxC,OAAO,IAAI,IAAI;cAC3F,IAAIyC,SAAS,GAAGrD,OAAO,CAACoD,aAAa,IAAI,IAAI,IAAIpD,OAAO,CAACoD,aAAa,CAACxC,OAAO,IAAI,IAAI;cAEtF,IAAI,CAACuC,UAAU,IAAIE,SAAS,EAAE;gBAC5B;gBACAtG,YAAY,CAACuC,GAAG,CAACmB,IAAI,CAAC;gBACtBxD,WAAW,CAACgG,MAAM,CAACxC,IAAI,CAAC;cAC1B,CAAC,MAAM,IAAI0C,UAAU,IAAIE,SAAS,EAAE,CAAC,KAAM,IAAIF,UAAU,IAAI,CAACE,SAAS,EAAE;gBACvE;gBACAtG,YAAY,CAACkG,MAAM,CAACxC,IAAI,CAAC;gBAEzB,IAAIL,QAAQ,EAAE;kBACZ;kBACAnD,WAAW,CAACqC,GAAG,CAACmB,IAAI,CAAC;gBACvB,CAAC,MAAM;kBACL3D,aAAa,CAACmG,MAAM,CAACxC,IAAI,CAAC;gBAC5B;cACF,CAAC,MAAM,IAAI,CAAC0C,UAAU,IAAI,CAACE,SAAS,EAAE;gBACpC,IAAIjD,QAAQ,EAAE;kBACZ;kBACAnD,WAAW,CAACqC,GAAG,CAACmB,IAAI,CAAC;gBACvB;cACF;YACF,CAAC,MAAM;cACL;cACA1D,YAAY,CAACuC,GAAG,CAACmB,IAAI,CAAC;YACxB;UACF,CAAC,CAAC;;UAGF,OAAOsC,oBAAoB,CAACD,KAAK,CAAC,IAAI,EAAE1B,SAAS,CAAC;QACpD,CAAC;MACH;IACF;IACA,SAASkC,sBAAsBA,CAAA,EAAG;MAChC;MACA,OAAO,KAAK;IACd,CAAC,CAAC;;IAEF,SAASC,oBAAoBA,CAAA,EAAG;MAC9B;QACE,OAAOxG,YAAY,CAACyG,IAAI;MAC1B;IACF,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,SAASC,mCAAmCA,CAAA,EAAG;MAC7C;QACE,IAAIC,SAAS;QACb,IAAIC,cAAc;QAClB,IAAIC,eAAe,GAAG,KAAK;QAC3B,OAAO,UAAUnF,IAAI,EAAEU,GAAG,EAAExB,UAAU,EAAEF,cAAc,EAAE;UACtD,IAAI,OAAO0B,GAAG,KAAK,QAAQ,EAAE;YAC3B;YACA;YACA;YACA,IAAI,CAACuE,SAAS,EAAE;cACd;cACAA,SAAS,GAAGjF,IAAI;cAChBkF,cAAc,GAAG,OAAOlG,cAAc,KAAK,UAAU;YACvD,CAAC,CAAC;YACF;YACA;;YAGA,IAAIgB,IAAI,IAAI,IAAI,KAAK,OAAOA,IAAI,KAAK,UAAU,IAAI,OAAOA,IAAI,KAAK,QAAQ,CAAC,EAAE;cAC5E0C,YAAY,CAAC1C,IAAI,EAAEU,GAAG,EAAExB,UAAU,EAAEF,cAAc,CAAC;YACrD;YAEA,OAAOgB,IAAI;UACb,CAAC,MAAM;YACL;YACA;YACA;YACA,IAAI,CAACmF,eAAe,IAAID,cAAc,EAAE;cACtCC,eAAe,GAAG,IAAI;cACtBvC,8BAA8B,CAACqC,SAAS,CAAC;YAC3C;UACF;QACF,CAAC;MACH;IACF;IACA,SAASG,qBAAqBA,CAACpF,IAAI,EAAE;MACnC;QACE,QAAQ,OAAOA,IAAI;UACjB,KAAK,UAAU;YACb;cACE;cACA,IAAIA,IAAI,CAACC,SAAS,IAAI,IAAI,EAAE;gBAC1B,IAAID,IAAI,CAACC,SAAS,CAACC,gBAAgB,EAAE;kBACnC;kBACA,OAAO,IAAI;gBACb;gBAEA,IAAImF,QAAQ,GAAGC,MAAM,CAACC,mBAAmB,CAACvF,IAAI,CAACC,SAAS,CAAC;gBAEzD,IAAIoF,QAAQ,CAACjG,MAAM,GAAG,CAAC,IAAIiG,QAAQ,CAAC,CAAC,CAAC,KAAK,aAAa,EAAE;kBACxD;kBACA,OAAO,KAAK;gBACd,CAAC,CAAC;;gBAGF,IAAIrF,IAAI,CAACC,SAAS,CAACuF,SAAS,KAAKF,MAAM,CAACrF,SAAS,EAAE;kBACjD;kBACA,OAAO,KAAK;gBACd,CAAC,CAAC;gBACF;cAEF,CAAC,CAAC;;cAGF,IAAIwF,IAAI,GAAGzF,IAAI,CAACyF,IAAI,IAAIzF,IAAI,CAAC0F,WAAW;cACxC,OAAO,OAAOD,IAAI,KAAK,QAAQ,IAAI,QAAQ,CAACE,IAAI,CAACF,IAAI,CAAC;YACxD;UAEF,KAAK,QAAQ;YACX;cACE,IAAIzF,IAAI,IAAI,IAAI,EAAE;gBAChB,QAAQc,WAAW,CAACd,IAAI,EAAE,UAAU,CAAC;kBACnC,KAAKjD,sBAAsB;kBAC3B,KAAKG,eAAe;oBAClB;oBACA,OAAO,IAAI;kBAEb;oBACE,OAAO,KAAK;gBAChB;cACF;cAEA,OAAO,KAAK;YACd;UAEF;YACE;cACE,OAAO,KAAK;YACd;QACJ;MACF;IACF;IAEA0I,OAAO,CAACd,oBAAoB,GAAGA,oBAAoB;IACnDc,OAAO,CAAChD,8BAA8B,GAAGA,8BAA8B;IACvEgD,OAAO,CAACZ,mCAAmC,GAAGA,mCAAmC;IACjFY,OAAO,CAAC7C,yBAAyB,GAAGA,yBAAyB;IAC7D6C,OAAO,CAAC/C,aAAa,GAAGA,aAAa;IACrC+C,OAAO,CAAC9C,eAAe,GAAGA,eAAe;IACzC8C,OAAO,CAACf,sBAAsB,GAAGA,sBAAsB;IACvDe,OAAO,CAACvC,oBAAoB,GAAGA,oBAAoB;IACnDuC,OAAO,CAACR,qBAAqB,GAAGA,qBAAqB;IACrDQ,OAAO,CAAC3E,mBAAmB,GAAGA,mBAAmB;IACjD2E,OAAO,CAACtD,QAAQ,GAAGA,QAAQ;IAC3BsD,OAAO,CAAClD,YAAY,GAAGA,YAAY;EACjC,CAAC,EAAE,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}