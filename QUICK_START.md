# 🚀 Quick Start Guide

## Prerequisites
- Node.js (v14+)
- MySQL (v5.7+)
- npm or yarn

## 1. Install Dependencies
```bash
# Install all dependencies
npm run install-all
```

## 2. Configure Database
```bash
# Copy environment file
cd backend
cp .env.example .env

# Edit .env with your MySQL credentials
# DB_PASSWORD=your_mysql_password
```

## 3. Setup Database
```bash
# Create database and tables
cd backend
node scripts/setup-database.js
```

## 4. Start the Application
```bash
# From root directory - starts both frontend and backend
npm run dev
```

## 5. Access the Application
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000

## 6. Test the Authentication
1. Go to http://localhost:3000/register
2. Create a new account
3. Login with your credentials
4. Access the dashboard

## Default Ports
- Frontend: 3000
- Backend: 5000
- MySQL: 3306

## Troubleshooting
- Ensure MySQL is running
- Check .env file configuration
- Verify ports are not in use
- Check console for errors

That's it! Your authentication system is ready to use! 🎉
