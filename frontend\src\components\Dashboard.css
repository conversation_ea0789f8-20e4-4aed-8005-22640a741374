/* Dashboard Component Styles */

.dashboard-container {
  min-height: 100vh;
  background: #f8fafc;
}

.dashboard-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 20px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h1 {
  color: #1a202c;
  font-size: 28px;
  font-weight: 700;
  margin: 0;
}

.logout-button {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-button:hover {
  background: #c53030;
  transform: translateY(-1px);
}

.dashboard-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-section h2 {
  color: #1a202c;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 10px;
}

.welcome-section p {
  color: #718096;
  font-size: 18px;
  margin: 0;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.dashboard-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  color: #1a202c;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.card-icon {
  font-size: 24px;
}

.card-content {
  padding: 20px 24px 24px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f7fafc;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  color: #718096;
  font-weight: 500;
  font-size: 14px;
}

.info-value {
  color: #1a202c;
  font-weight: 600;
  font-size: 14px;
}

.status-active {
  color: #38a169 !important;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  color: #1a202c;
  font-weight: 500;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #cbd5e0;
}

.status-indicator.active {
  background: #38a169;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #1a202c;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-button:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateX(4px);
}

.action-button:last-child {
  margin-bottom: 0;
}

.action-icon {
  font-size: 16px;
}

.dashboard-footer {
  text-align: center;
  padding: 40px 20px;
  border-top: 1px solid #e2e8f0;
  margin-top: 40px;
}

.dashboard-footer p {
  color: #718096;
  font-size: 14px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .dashboard-main {
    padding: 30px 15px;
  }

  .welcome-section h2 {
    font-size: 28px;
  }

  .welcome-section p {
    font-size: 16px;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .card-header,
  .card-content {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .dashboard-main {
    padding: 20px 10px;
  }

  .welcome-section h2 {
    font-size: 24px;
  }

  .card-header,
  .card-content {
    padding: 12px 16px;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
