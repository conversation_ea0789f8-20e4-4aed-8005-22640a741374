{"name": "mysql-auth-project", "version": "1.0.0", "description": "Full-stack authentication system with MySQL, Node.js, Express, and React", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm start", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install", "setup": "npm run install-all && cd backend && node scripts/setup-database.js", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "test-api": "cd backend && curl http://localhost:5000/health"}, "keywords": ["authentication", "mysql", "nodejs", "express", "react"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}