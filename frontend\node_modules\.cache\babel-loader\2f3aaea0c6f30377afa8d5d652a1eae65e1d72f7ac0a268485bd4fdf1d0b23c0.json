{"ast": null, "code": "var _jsxFileName = \"D:\\\\mysql-project\\\\frontend\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport './Auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const {\n    login,\n    loading,\n    error,\n    isAuthenticated,\n    clearError\n  } = useAuth();\n  const navigate = useNavigate();\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/dashboard');\n    }\n  }, [isAuthenticated, navigate]);\n\n  // Clear error when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const result = await login(formData.email, formData.password);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Sign in to your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"auth-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            children: \"Email Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleChange,\n            required: true,\n            placeholder: \"Enter your email\",\n            className: \"form-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"password-input-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: showPassword ? 'text' : 'password',\n              id: \"password\",\n              name: \"password\",\n              value: formData.password,\n              onChange: handleChange,\n              required: true,\n              placeholder: \"Enter your password\",\n              className: \"form-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"password-toggle\",\n              onClick: () => setShowPassword(!showPassword),\n              children: showPassword ? '👁️' : '👁️‍🗨️'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          className: \"auth-button\",\n          children: loading ? 'Signing In...' : 'Sign In'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"auth-link\",\n            children: \"Sign up here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"FpwbfiVlitorWbClLvSzXrT+xPE=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "formData", "setFormData", "email", "password", "showPassword", "setShowPassword", "login", "loading", "error", "isAuthenticated", "clearError", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "result", "success", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "placeholder", "onClick", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["D:/mysql-project/frontend/src/components/Login.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport './Auth.css';\n\nconst Login = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  \n  const { login, loading, error, isAuthenticated, clearError } = useAuth();\n  const navigate = useNavigate();\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/dashboard');\n    }\n  }, [isAuthenticated, navigate]);\n\n  // Clear error when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    const result = await login(formData.email, formData.password);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-card\">\n        <div className=\"auth-header\">\n          <h2>Welcome Back</h2>\n          <p>Sign in to your account</p>\n        </div>\n\n        {error && (\n          <div className=\"error-message\">\n            {error}\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit} className=\"auth-form\">\n          <div className=\"form-group\">\n            <label htmlFor=\"email\">Email Address</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              required\n              placeholder=\"Enter your email\"\n              className=\"form-input\"\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\">Password</label>\n            <div className=\"password-input-container\">\n              <input\n                type={showPassword ? 'text' : 'password'}\n                id=\"password\"\n                name=\"password\"\n                value={formData.password}\n                onChange={handleChange}\n                required\n                placeholder=\"Enter your password\"\n                className=\"form-input\"\n              />\n              <button\n                type=\"button\"\n                className=\"password-toggle\"\n                onClick={() => setShowPassword(!showPassword)}\n              >\n                {showPassword ? '👁️' : '👁️‍🗨️'}\n              </button>\n            </div>\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"auth-button\"\n          >\n            {loading ? 'Signing In...' : 'Sign In'}\n          </button>\n        </form>\n\n        <div className=\"auth-footer\">\n          <p>\n            Don't have an account?{' '}\n            <Link to=\"/register\" className=\"auth-link\">\n              Sign up here\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvCW,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM;IAAEe,KAAK;IAAEC,OAAO;IAAEC,KAAK;IAAEC,eAAe;IAAEC;EAAW,CAAC,GAAGf,OAAO,CAAC,CAAC;EACxE,MAAMgB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd,IAAIiB,eAAe,EAAE;MACnBE,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACF,eAAe,EAAEE,QAAQ,CAAC,CAAC;;EAE/B;EACAnB,SAAS,CAAC,MAAM;IACdkB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAME,YAAY,GAAIC,CAAC,IAAK;IAC1BZ,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACa,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,MAAMC,MAAM,GAAG,MAAMb,KAAK,CAACN,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,QAAQ,CAAC;IAC7D,IAAIgB,MAAM,CAACC,OAAO,EAAE;MAClBT,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,oBACEd,OAAA;IAAKwB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BzB,OAAA;MAAKwB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBzB,OAAA;QAAKwB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzB,OAAA;UAAAyB,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrB7B,OAAA;UAAAyB,QAAA,EAAG;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,EAELlB,KAAK,iBACJX,OAAA;QAAKwB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3Bd;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED7B,OAAA;QAAM8B,QAAQ,EAAEV,YAAa;QAACI,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACjDzB,OAAA;UAAKwB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzB,OAAA;YAAO+B,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5C7B,OAAA;YACEgC,IAAI,EAAC,OAAO;YACZC,EAAE,EAAC,OAAO;YACVf,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEhB,QAAQ,CAACE,KAAM;YACtB6B,QAAQ,EAAEnB,YAAa;YACvBoB,QAAQ;YACRC,WAAW,EAAC,kBAAkB;YAC9BZ,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN7B,OAAA;UAAKwB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzB,OAAA;YAAO+B,OAAO,EAAC,UAAU;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1C7B,OAAA;YAAKwB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCzB,OAAA;cACEgC,IAAI,EAAEzB,YAAY,GAAG,MAAM,GAAG,UAAW;cACzC0B,EAAE,EAAC,UAAU;cACbf,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEhB,QAAQ,CAACG,QAAS;cACzB4B,QAAQ,EAAEnB,YAAa;cACvBoB,QAAQ;cACRC,WAAW,EAAC,qBAAqB;cACjCZ,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACF7B,OAAA;cACEgC,IAAI,EAAC,QAAQ;cACbR,SAAS,EAAC,iBAAiB;cAC3Ba,OAAO,EAAEA,CAAA,KAAM7B,eAAe,CAAC,CAACD,YAAY,CAAE;cAAAkB,QAAA,EAE7ClB,YAAY,GAAG,KAAK,GAAG;YAAS;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7B,OAAA;UACEgC,IAAI,EAAC,QAAQ;UACbM,QAAQ,EAAE5B,OAAQ;UAClBc,SAAS,EAAC,aAAa;UAAAC,QAAA,EAEtBf,OAAO,GAAG,eAAe,GAAG;QAAS;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP7B,OAAA;QAAKwB,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BzB,OAAA;UAAAyB,QAAA,GAAG,wBACqB,EAAC,GAAG,eAC1BzB,OAAA,CAACJ,IAAI;YAAC2C,EAAE,EAAC,WAAW;YAACf,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAE3C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA7GID,KAAK;EAAA,QAOsDH,OAAO,EACrDD,WAAW;AAAA;AAAA2C,EAAA,GARxBvC,KAAK;AA+GX,eAAeA,KAAK;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}