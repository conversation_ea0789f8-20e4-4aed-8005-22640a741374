{"ast": null, "code": "var _jsxFileName = \"D:\\\\mysql-project\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\n// API base URL\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Auth context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    if (token) {\n      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete api.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Load user on app start - ONLY ONCE\n  useEffect(() => {\n    const loadUser = async () => {\n      const storedToken = localStorage.getItem('token');\n      if (storedToken) {\n        try {\n          api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;\n          const response = await api.get('/auth/profile');\n          setUser(response.data.data.user);\n          setToken(storedToken);\n        } catch (error) {\n          console.error('Failed to load user:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          setUser(null);\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n    loadUser();\n  }, []); // Empty dependency array - only run once\n\n  // Login function\n  const login = async (email, password) => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await api.post('/auth/login', {\n        email,\n        password\n      });\n      const {\n        user: userData,\n        token: userToken,\n        refreshToken\n      } = response.data.data;\n      localStorage.setItem('token', userToken);\n      if (refreshToken) {\n        localStorage.setItem('refreshToken', refreshToken);\n      }\n      setUser(userData);\n      setToken(userToken);\n      setLoading(false);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed';\n      setError(errorMessage);\n      setLoading(false);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Register function\n  const register = async userData => {\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await api.post('/auth/register', userData);\n      const {\n        user: newUser,\n        token: userToken,\n        refreshToken\n      } = response.data.data;\n      localStorage.setItem('token', userToken);\n      if (refreshToken) {\n        localStorage.setItem('refreshToken', refreshToken);\n      }\n      setUser(newUser);\n      setToken(userToken);\n      setLoading(false);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Registration failed';\n      setError(errorMessage);\n      setLoading(false);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      setUser(null);\n      setToken(null);\n      setError(null);\n      delete api.defaults.headers.common['Authorization'];\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    setError(null);\n  };\n  const value = {\n    user,\n    token,\n    isAuthenticated: !!user,\n    loading,\n    error,\n    login,\n    register,\n    logout,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"iLpaRsBcvK5kw9CoUYngETMeKsY=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "token", "setToken", "localStorage", "getItem", "loading", "setLoading", "error", "setError", "defaults", "common", "loadUser", "storedToken", "response", "get", "data", "console", "removeItem", "login", "email", "password", "post", "userData", "userToken", "refreshToken", "setItem", "success", "_error$response", "_error$response$data", "errorMessage", "message", "register", "newUser", "_error$response2", "_error$response2$data", "logout", "clearError", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/mysql-project/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\n\n// API base URL\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Auth context\nconst AuthContext = createContext();\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    if (token) {\n      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete api.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Load user on app start - ONLY ONCE\n  useEffect(() => {\n    const loadUser = async () => {\n      const storedToken = localStorage.getItem('token');\n      \n      if (storedToken) {\n        try {\n          api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;\n          const response = await api.get('/auth/profile');\n          setUser(response.data.data.user);\n          setToken(storedToken);\n        } catch (error) {\n          console.error('Failed to load user:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          setUser(null);\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n\n    loadUser();\n  }, []); // Empty dependency array - only run once\n\n  // Login function\n  const login = async (email, password) => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await api.post('/auth/login', { email, password });\n      const { user: userData, token: userToken, refreshToken } = response.data.data;\n      \n      localStorage.setItem('token', userToken);\n      if (refreshToken) {\n        localStorage.setItem('refreshToken', refreshToken);\n      }\n      \n      setUser(userData);\n      setToken(userToken);\n      setLoading(false);\n      \n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Login failed';\n      setError(errorMessage);\n      setLoading(false);\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Register function\n  const register = async (userData) => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await api.post('/auth/register', userData);\n      const { user: newUser, token: userToken, refreshToken } = response.data.data;\n      \n      localStorage.setItem('token', userToken);\n      if (refreshToken) {\n        localStorage.setItem('refreshToken', refreshToken);\n      }\n      \n      setUser(newUser);\n      setToken(userToken);\n      setLoading(false);\n      \n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Registration failed';\n      setError(errorMessage);\n      setLoading(false);\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      setUser(null);\n      setToken(null);\n      setError(null);\n      delete api.defaults.headers.common['Authorization'];\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    setError(null);\n  };\n\n  const value = {\n    user,\n    token,\n    isAuthenticated: !!user,\n    loading,\n    error,\n    login,\n    register,\n    logout,\n    clearError\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,WAAW,gBAAGf,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAMgB,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAACqB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;EACjE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,IAAIkB,KAAK,EAAE;MACTV,GAAG,CAACkB,QAAQ,CAACf,OAAO,CAACgB,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUT,KAAK,EAAE;IAClE,CAAC,MAAM;MACL,OAAOV,GAAG,CAACkB,QAAQ,CAACf,OAAO,CAACgB,MAAM,CAAC,eAAe,CAAC;IACrD;EACF,CAAC,EAAE,CAACT,KAAK,CAAC,CAAC;;EAEX;EACAlB,SAAS,CAAC,MAAM;IACd,MAAM4B,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,MAAMC,WAAW,GAAGT,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAEjD,IAAIQ,WAAW,EAAE;QACf,IAAI;UACFrB,GAAG,CAACkB,QAAQ,CAACf,OAAO,CAACgB,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUE,WAAW,EAAE;UACtE,MAAMC,QAAQ,GAAG,MAAMtB,GAAG,CAACuB,GAAG,CAAC,eAAe,CAAC;UAC/Cd,OAAO,CAACa,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAChB,IAAI,CAAC;UAChCG,QAAQ,CAACU,WAAW,CAAC;QACvB,CAAC,CAAC,OAAOL,KAAK,EAAE;UACdS,OAAO,CAACT,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5CJ,YAAY,CAACc,UAAU,CAAC,OAAO,CAAC;UAChCd,YAAY,CAACc,UAAU,CAAC,cAAc,CAAC;UACvCjB,OAAO,CAAC,IAAI,CAAC;UACbE,QAAQ,CAAC,IAAI,CAAC;QAChB;MACF;MACAI,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDK,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMO,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvCd,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMtB,GAAG,CAAC8B,IAAI,CAAC,aAAa,EAAE;QAAEF,KAAK;QAAEC;MAAS,CAAC,CAAC;MACnE,MAAM;QAAErB,IAAI,EAAEuB,QAAQ;QAAErB,KAAK,EAAEsB,SAAS;QAAEC;MAAa,CAAC,GAAGX,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE7EZ,YAAY,CAACsB,OAAO,CAAC,OAAO,EAAEF,SAAS,CAAC;MACxC,IAAIC,YAAY,EAAE;QAChBrB,YAAY,CAACsB,OAAO,CAAC,cAAc,EAAED,YAAY,CAAC;MACpD;MAEAxB,OAAO,CAACsB,QAAQ,CAAC;MACjBpB,QAAQ,CAACqB,SAAS,CAAC;MACnBjB,UAAU,CAAC,KAAK,CAAC;MAEjB,OAAO;QAAEoB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOnB,KAAK,EAAE;MAAA,IAAAoB,eAAA,EAAAC,oBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAApB,KAAK,CAACM,QAAQ,cAAAc,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBZ,IAAI,cAAAa,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAI,cAAc;MACpEtB,QAAQ,CAACqB,YAAY,CAAC;MACtBvB,UAAU,CAAC,KAAK,CAAC;MACjB,OAAO;QAAEoB,OAAO,EAAE,KAAK;QAAEnB,KAAK,EAAEsB;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAME,QAAQ,GAAG,MAAOT,QAAQ,IAAK;IACnChB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMtB,GAAG,CAAC8B,IAAI,CAAC,gBAAgB,EAAEC,QAAQ,CAAC;MAC3D,MAAM;QAAEvB,IAAI,EAAEiC,OAAO;QAAE/B,KAAK,EAAEsB,SAAS;QAAEC;MAAa,CAAC,GAAGX,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE5EZ,YAAY,CAACsB,OAAO,CAAC,OAAO,EAAEF,SAAS,CAAC;MACxC,IAAIC,YAAY,EAAE;QAChBrB,YAAY,CAACsB,OAAO,CAAC,cAAc,EAAED,YAAY,CAAC;MACpD;MAEAxB,OAAO,CAACgC,OAAO,CAAC;MAChB9B,QAAQ,CAACqB,SAAS,CAAC;MACnBjB,UAAU,CAAC,KAAK,CAAC;MAEjB,OAAO;QAAEoB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOnB,KAAK,EAAE;MAAA,IAAA0B,gBAAA,EAAAC,qBAAA;MACd,MAAML,YAAY,GAAG,EAAAI,gBAAA,GAAA1B,KAAK,CAACM,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlB,IAAI,cAAAmB,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,qBAAqB;MAC3EtB,QAAQ,CAACqB,YAAY,CAAC;MACtBvB,UAAU,CAAC,KAAK,CAAC;MACjB,OAAO;QAAEoB,OAAO,EAAE,KAAK;QAAEnB,KAAK,EAAEsB;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMM,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAM5C,GAAG,CAAC8B,IAAI,CAAC,cAAc,CAAC;IAChC,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRJ,YAAY,CAACc,UAAU,CAAC,OAAO,CAAC;MAChCd,YAAY,CAACc,UAAU,CAAC,cAAc,CAAC;MACvCjB,OAAO,CAAC,IAAI,CAAC;MACbE,QAAQ,CAAC,IAAI,CAAC;MACdM,QAAQ,CAAC,IAAI,CAAC;MACd,OAAOjB,GAAG,CAACkB,QAAQ,CAACf,OAAO,CAACgB,MAAM,CAAC,eAAe,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAM0B,UAAU,GAAGA,CAAA,KAAM;IACvB5B,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAM6B,KAAK,GAAG;IACZtC,IAAI;IACJE,KAAK;IACLqC,eAAe,EAAE,CAAC,CAACvC,IAAI;IACvBM,OAAO;IACPE,KAAK;IACLW,KAAK;IACLa,QAAQ;IACRI,MAAM;IACNC;EACF,CAAC;EAED,oBACElD,OAAA,CAACS,WAAW,CAAC4C,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAxC,QAAA,EAChCA;EAAQ;IAAA2C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAA7C,EAAA,CAtIaF,YAAY;AAAAgD,EAAA,GAAZhD,YAAY;AAuIzB,OAAO,MAAMiD,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGlE,UAAU,CAACc,WAAW,CAAC;EACvC,IAAI,CAACoD,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAelD,WAAW;AAAC,IAAAiD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}