const { initializeDatabase } = require('../config/database');

async function setupDatabase() {
  console.log('🚀 Starting database setup...');

  try {
    // Initialize database and tables (this will create the database if it doesn't exist)
    const initSuccess = await initializeDatabase();
    if (!initSuccess) {
      console.error('❌ Database initialization failed.');
      process.exit(1);
    }

    console.log('✅ Database setup completed successfully!');
    console.log('\n📋 Database Setup Summary:');
    console.log('- Database created/verified');
    console.log('- Users table created with proper schema');
    console.log('- Indexes added for performance');
    console.log('\n🎉 Your authentication system is ready to use!');

    process.exit(0);
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    process.exit(1);
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
