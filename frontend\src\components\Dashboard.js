import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import './Dashboard.css';

const Dashboard = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="dashboard-container">
      <header className="dashboard-header">
        <div className="header-content">
          <h1>Dashboard</h1>
          <button onClick={handleLogout} className="logout-button">
            Logout
          </button>
        </div>
      </header>

      <main className="dashboard-main">
        <div className="welcome-section">
          <h2>Welcome back, {user?.first_name || user?.username}! 👋</h2>
          <p>You have successfully logged into your account.</p>
        </div>

        <div className="dashboard-grid">
          <div className="dashboard-card">
            <div className="card-header">
              <h3>Profile Information</h3>
              <span className="card-icon">👤</span>
            </div>
            <div className="card-content">
              <div className="info-row">
                <span className="info-label">Username:</span>
                <span className="info-value">{user?.username}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Email:</span>
                <span className="info-value">{user?.email}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Full Name:</span>
                <span className="info-value">
                  {user?.first_name && user?.last_name 
                    ? `${user.first_name} ${user.last_name}`
                    : 'Not provided'
                  }
                </span>
              </div>
              <div className="info-row">
                <span className="info-label">Member Since:</span>
                <span className="info-value">{formatDate(user?.created_at)}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Last Login:</span>
                <span className="info-value">{formatDate(user?.last_login)}</span>
              </div>
            </div>
          </div>

          <div className="dashboard-card">
            <div className="card-header">
              <h3>Account Status</h3>
              <span className="card-icon">✅</span>
            </div>
            <div className="card-content">
              <div className="status-item">
                <span className="status-indicator active"></span>
                <span>Account Active</span>
              </div>
              <div className="status-item">
                <span className="status-indicator active"></span>
                <span>Email Verified</span>
              </div>
              <div className="status-item">
                <span className="status-indicator active"></span>
                <span>Authentication Enabled</span>
              </div>
            </div>
          </div>

          <div className="dashboard-card">
            <div className="card-header">
              <h3>Quick Actions</h3>
              <span className="card-icon">⚡</span>
            </div>
            <div className="card-content">
              <button className="action-button">
                <span className="action-icon">🔧</span>
                Edit Profile
              </button>
              <button className="action-button">
                <span className="action-icon">🔒</span>
                Change Password
              </button>
              <button className="action-button">
                <span className="action-icon">⚙️</span>
                Account Settings
              </button>
            </div>
          </div>

          <div className="dashboard-card">
            <div className="card-header">
              <h3>System Information</h3>
              <span className="card-icon">📊</span>
            </div>
            <div className="card-content">
              <div className="info-row">
                <span className="info-label">User ID:</span>
                <span className="info-value">{user?.id}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Session Status:</span>
                <span className="info-value status-active">Active</span>
              </div>
              <div className="info-row">
                <span className="info-label">Authentication:</span>
                <span className="info-value status-active">JWT Token</span>
              </div>
            </div>
          </div>
        </div>

        <div className="dashboard-footer">
          <p>This is a demo authentication system built with React, Node.js, Express, and MySQL.</p>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
