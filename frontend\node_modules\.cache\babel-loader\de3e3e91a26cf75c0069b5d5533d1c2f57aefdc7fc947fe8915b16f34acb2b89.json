{"ast": null, "code": "var _jsxFileName = \"D:\\\\mysql-project\\\\frontend\\\\src\\\\components\\\\PrivateRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PrivateRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    loading\n  } = useAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        background: '#f8fafc'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          gap: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '40px',\n            height: '40px',\n            border: '4px solid #e2e8f0',\n            borderTop: '4px solid #667eea',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#718096',\n            fontSize: '16px',\n            margin: 0\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n            @keyframes spin {\n              0% { transform: rotate(0deg); }\n              100% { transform: rotate(360deg); }\n            }\n          `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Render protected component if authenticated\n  return children;\n};\n_s(PrivateRoute, \"fNj96oVmPd4sFazcimgf9N7S8ao=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = PrivateRoute;\nexport default PrivateRoute;\nvar _c;\n$RefreshReg$(_c, \"PrivateRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "PrivateRoute", "children", "_s", "isAuthenticated", "loading", "location", "style", "display", "justifyContent", "alignItems", "height", "background", "flexDirection", "gap", "width", "border", "borderTop", "borderRadius", "animation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "fontSize", "margin", "to", "state", "from", "replace", "_c", "$RefreshReg$"], "sources": ["D:/mysql-project/frontend/src/components/PrivateRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst PrivateRoute = ({ children }) => {\n  const { isAuthenticated, loading } = useAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        background: '#f8fafc'\n      }}>\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          gap: '20px'\n        }}>\n          <div style={{\n            width: '40px',\n            height: '40px',\n            border: '4px solid #e2e8f0',\n            borderTop: '4px solid #667eea',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite'\n          }}></div>\n          <p style={{\n            color: '#718096',\n            fontSize: '16px',\n            margin: 0\n          }}>\n            Loading...\n          </p>\n        </div>\n        <style>\n          {`\n            @keyframes spin {\n              0% { transform: rotate(0deg); }\n              100% { transform: rotate(360deg); }\n            }\n          `}\n        </style>\n      </div>\n    );\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // Render protected component if authenticated\n  return children;\n};\n\nexport default PrivateRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrC,MAAM;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC9C,MAAMQ,QAAQ,GAAGT,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIQ,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKO,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,UAAU,EAAE;MACd,CAAE;MAAAV,QAAA,gBACAF,OAAA;QAAKO,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfK,aAAa,EAAE,QAAQ;UACvBH,UAAU,EAAE,QAAQ;UACpBI,GAAG,EAAE;QACP,CAAE;QAAAZ,QAAA,gBACAF,OAAA;UAAKO,KAAK,EAAE;YACVQ,KAAK,EAAE,MAAM;YACbJ,MAAM,EAAE,MAAM;YACdK,MAAM,EAAE,mBAAmB;YAC3BC,SAAS,EAAE,mBAAmB;YAC9BC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE;UACb;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACTvB,OAAA;UAAGO,KAAK,EAAE;YACRiB,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChBC,MAAM,EAAE;UACV,CAAE;UAAAxB,QAAA,EAAC;QAEH;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNvB,OAAA;QAAAE,QAAA,EACG;AACX;AACA;AACA;AACA;AACA;MAAW;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;;EAEA;EACA,IAAI,CAACnB,eAAe,EAAE;IACpB,oBAAOJ,OAAA,CAACJ,QAAQ;MAAC+B,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAEvB;MAAS,CAAE;MAACwB,OAAO;IAAA;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;;EAEA;EACA,OAAOrB,QAAQ;AACjB,CAAC;AAACC,EAAA,CAvDIF,YAAY;EAAA,QACqBH,OAAO,EAC3BD,WAAW;AAAA;AAAAkC,EAAA,GAFxB9B,YAAY;AAyDlB,eAAeA,YAAY;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}